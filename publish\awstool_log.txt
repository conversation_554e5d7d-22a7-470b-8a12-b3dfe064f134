2025-07-30 21:26:19 线程3：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 5 (进度: 100%)
2025-07-30 21:26:19 线程3：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-07-30 21:26:19 线程3：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-07-30 21:26:19 线程3：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-07-30 21:26:19 线程3：[信息] [信息] 🎯 找到匹配按钮: 'Continue (step 4 of 5)' → 第7页 (进度: 100%)
2025-07-30 21:26:19 线程3：[信息] [信息] ✅ 直接确认为第7页 (进度: 100%)
2025-07-30 21:26:19 线程3：[信息] [信息]  智能检测到当前在第7页 (进度: 100%)
2025-07-30 21:26:19 线程3：[信息] [信息] 智能检测到当前在第7页，开始智能处理... (进度: 100%)
2025-07-30 21:26:19 线程3：[信息] 已继续
2025-07-30 21:26:19 [信息] 线程3已继续
2025-07-30 21:26:24 线程3：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-07-30 21:26:24 线程3：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-07-30 21:26:24 线程3：[信息] 已暂停
2025-07-30 21:26:24 [信息] 线程3已暂停
2025-07-30 21:26:24 [信息] 线程3已暂停
2025-07-30 21:26:39 [信息] 获取线程3当前数据: <EMAIL>
2025-07-30 21:26:39 线程3：[信息] 终止时正在处理的数据: <EMAIL>
2025-07-30 21:26:39 线程3：[信息] 数据详情: <EMAIL>|Y5CiVYLv|Mary Matheson|Bradesco|Rua Ouro Branco 1399|Aparecida de Goiania|GO|74974-460|5552700147998880|03|28|154|Mary Matheson|1950134184273448990|BR
2025-07-30 21:26:39 线程3：[信息] 多线程终止 - 复制密钥信息到剪贴板
2025-07-30 21:26:39 线程3：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：1950134184273448990 ③AWS密码：Y5CiVYLv ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-07-30 21:26:39 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：1950134184273448990 ③AWS密码：Y5CiVYLv ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 21:26:39 线程3：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：1950134184273448990 ③AWS密码：Y5CiVYLv ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 21:26:39 [信息] 线程3请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：1950134184273448990 ③AWS密码：Y5CiVYLv ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 21:26:39 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：1950134184273448990 ③AWS密码：Y5CiVYLv ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 21:26:39 [信息] 线程3剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：1950134184273448990 ③AWS密码：Y5CiVYLv ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 21:26:39 [信息] 所有线程已完成，通知主窗口重置状态
2025-07-30 21:26:39 [信息] 多线程状态已重置
2025-07-30 21:26:39 [信息] 所有线程已完成，通知主窗口重置状态
2025-07-30 21:26:39 [信息] 多线程状态已重置
2025-07-30 21:26:39 线程3：[信息] [信息] 注册已终止 (进度: 100%)
2025-07-30 21:26:39 [信息] 所有线程已完成，通知主窗口重置状态
2025-07-30 21:26:39 [信息] 多线程状态已重置
2025-07-30 21:26:39 线程3：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-07-30 21:26:39 [信息] 所有线程已完成，通知主窗口重置状态
2025-07-30 21:26:39 [信息] 多线程状态已重置
2025-07-30 21:26:39 线程3：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：1950134184273448990 ③AWS密码：Y5CiVYLv ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-07-30 21:26:39 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：1950134184273448990 ③AWS密码：Y5CiVYLv ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 21:26:39 线程3：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：1950134184273448990 ③AWS密码：Y5CiVYLv ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 21:26:39 [信息] 线程3请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：1950134184273448990 ③AWS密码：Y5CiVYLv ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 21:26:39 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：1950134184273448990 ③AWS密码：Y5CiVYLv ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 21:26:39 [信息] 线程3剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：1950134184273448990 ③AWS密码：Y5CiVYLv ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 21:26:39 [信息] 所有线程已完成，通知主窗口重置状态
2025-07-30 21:26:39 [信息] 多线程状态已重置
2025-07-30 21:26:39 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-07-30 21:26:39 线程3：[信息] 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\AWS_Thread_3_20250730_210156
2025-07-30 21:26:39 [信息] 所有线程已完成，通知主窗口重置状态
2025-07-30 21:26:39 [信息] 多线程状态已重置
2025-07-30 21:26:39 线程3：[信息] 已终止
2025-07-30 21:26:39 [信息] 线程3已终止
2025-07-30 21:26:39 [信息] 开始处理线程3终止数据，共1个数据
2025-07-30 21:26:39 [信息] 处理线程3终止数据: <EMAIL>
2025-07-30 21:26:39 [信息] 从注册数据列表中移除: <EMAIL>
2025-07-30 21:26:39 [信息] 线程3终止 - 数据已移动到终止列表: <EMAIL>
2025-07-30 21:26:40 [信息] 线程3终止数据处理完成，成功移动1个数据
2025-07-30 21:26:40 [信息] UniformGrid列数已更新为: 1
2025-07-30 21:26:40 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-07-30 21:26:40 [信息] 线程3已终止
2025-07-30 21:26:43 [信息] 多线程窗口引用已清理
2025-07-30 21:26:43 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-07-30 21:26:43 [信息] 多线程管理窗口正在关闭
2025-07-30 21:32:43 [按钮操作] 开始注册 -> 启动注册流程
2025-07-30 21:32:43 [信息] 开始启动多线程注册，线程数量: 3
2025-07-30 21:32:43 [信息] 多线程管理器已重新创建
2025-07-30 21:32:43 [信息] 开始启动多线程注册，线程数量: 3，数据条数: 8
2025-07-30 21:32:43 [信息] 所有线程已停止并清理
2025-07-30 21:32:43 [信息] 正在初始化多线程服务...
2025-07-30 21:32:43 [信息] 手机号码管理器已初始化，将在第一个线程完成第二页后获取手机号码
2025-07-30 21:32:43 [信息] 多线程服务初始化完成
2025-07-30 21:32:43 [信息] 数据分配完成：共8条数据分配给3个线程
2025-07-30 21:32:43 [信息] 线程1分配到3条数据
2025-07-30 21:32:43 [信息] 线程2分配到3条数据
2025-07-30 21:32:43 [信息] 线程3分配到2条数据
2025-07-30 21:32:43 [信息] 屏幕工作区域: 1280x672
2025-07-30 21:32:43 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x200), 列1行1, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-07-30 21:32:43 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-07-30 21:32:43 线程1：[信息] 添加数据到队列: <EMAIL>
2025-07-30 21:32:43 线程1：[信息] 添加数据到队列: <EMAIL>
2025-07-30 21:32:43 线程1：[信息] 添加数据到队列: <EMAIL>
2025-07-30 21:32:43 [信息] 线程1已创建，窗口位置: (0, 0)
2025-07-30 21:32:43 [信息] 屏幕工作区域: 1280x672
2025-07-30 21:32:43 [信息] 线程2窗口布局: 位置(0, 219), 大小(384x200), 列1行2, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-07-30 21:32:43 线程2：[信息] 已创建，窗口位置: (0, 219)
2025-07-30 21:32:43 线程2：[信息] 添加数据到队列: <EMAIL>
2025-07-30 21:32:43 线程2：[信息] 添加数据到队列: <EMAIL>
2025-07-30 21:32:43 线程2：[信息] 添加数据到队列: <EMAIL>
2025-07-30 21:32:43 [信息] 线程2已创建，窗口位置: (0, 219)
2025-07-30 21:32:43 [信息] 屏幕工作区域: 1280x672
2025-07-30 21:32:43 [信息] 线程3窗口布局: 位置(0, 438), 大小(384x200), 列1行3, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-07-30 21:32:43 线程3：[信息] 已创建，窗口位置: (0, 438)
2025-07-30 21:32:43 线程3：[信息] 添加数据到队列: <EMAIL>
2025-07-30 21:32:43 线程3：[信息] 添加数据到队列: <EMAIL>
2025-07-30 21:32:43 [信息] 线程3已创建，窗口位置: (0, 438)
2025-07-30 21:32:43 [信息] 多线程注册启动成功，共3个线程
2025-07-30 21:32:43 线程1：[信息] 开始启动注册流程
2025-07-30 21:32:43 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-07-30 21:32:43 线程1：[信息] 启动无痕Chrome浏览器...
2025-07-30 21:32:43 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-07-30 21:32:43 [信息] 多线程管理窗口已初始化
2025-07-30 21:32:43 [信息] UniformGrid列数已更新为: 1
2025-07-30 21:32:43 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-07-30 21:32:43 [信息] 多线程管理窗口已打开
2025-07-30 21:32:43 [信息] 多线程注册启动成功，共3个线程
2025-07-30 21:32:44 [信息] UniformGrid列数已更新为: 1
2025-07-30 21:32:44 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-07-30 21:32:44 线程2：[信息] 开始启动注册流程
2025-07-30 21:32:44 线程2：[信息] 开始启动浏览器: 位置(0, 219), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-07-30 21:32:44 线程2：[信息] 启动无痕Chrome浏览器...
2025-07-30 21:32:44 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-07-30 21:32:45 [信息] UniformGrid列数已更新为: 1
2025-07-30 21:32:45 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-07-30 21:32:45 线程3：[信息] 开始启动注册流程
2025-07-30 21:32:45 线程3：[信息] 开始启动浏览器: 位置(0, 438), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-30 21:32:45 线程3：[信息] 启动无痕Chrome浏览器...
2025-07-30 21:32:45 线程3：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-07-30 21:32:51 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0
2025-07-30 21:32:51 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-07-30 21:32:51 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0
2025-07-30 21:32:51 [信息] UniformGrid列数已更新为: 2
2025-07-30 21:32:51 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 3, 列数: 2
2025-07-30 21:32:51 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-07-30 21:32:51 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0
2025-07-30 21:32:51 线程3：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-07-30 21:32:52 线程1：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-07-30 21:32:52 线程2：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-07-30 21:32:52 线程3：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-07-30 21:32:55 线程1：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-07-30 21:32:56 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 0%)
2025-07-30 21:32:56 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-07-30 21:32:56 线程1：[信息] 浏览器启动成功
2025-07-30 21:32:56 线程1：[信息] 获取下一个数据: <EMAIL>
2025-07-30 21:32:56 线程1：[信息] 开始处理账户: <EMAIL>
2025-07-30 21:32:56 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-07-30 21:32:56 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-07-30 21:32:56 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-07-30 21:32:56 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-07-30 21:32:56 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-07-30 21:32:56 线程3：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-07-30 21:32:56 线程2：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-07-30 21:32:56 线程3：[信息] [信息] 已设置浏览器标题: 线程3 - AWS注册工具 (进度: 0%)
2025-07-30 21:32:56 线程3：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-07-30 21:32:56 线程3：[信息] 浏览器启动成功
2025-07-30 21:32:56 线程3：[信息] 获取下一个数据: <EMAIL>
2025-07-30 21:32:56 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 0%)
2025-07-30 21:32:57 线程3：[信息] 开始处理账户: <EMAIL>
2025-07-30 21:32:57 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-07-30 21:32:57 线程2：[信息] 浏览器启动成功
2025-07-30 21:32:57 线程2：[信息] 获取下一个数据: <EMAIL>
2025-07-30 21:32:57 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-07-30 21:32:57 线程2：[信息] 开始处理账户: <EMAIL>
2025-07-30 21:32:57 线程3：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-07-30 21:32:57 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-07-30 21:32:57 线程1：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-07-30 21:32:57 线程3：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-07-30 21:32:57 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-07-30 21:32:57 线程3：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-07-30 21:32:57 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-07-30 21:32:57 线程3：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-07-30 21:32:57 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-07-30 21:32:57 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-07-30 21:32:57 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-07-30 21:32:57 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-07-30 21:32:57 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-07-30 21:32:57 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-07-30 21:32:57 线程2：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-07-30 21:32:57 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-07-30 21:32:57 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-07-30 21:32:57 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0... (进度: 98%)
2025-07-30 21:32:57 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-07-30 21:32:58 线程3：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-07-30 21:32:58 线程3：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-07-30 21:32:58 线程3：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-07-30 21:32:58 线程3：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0... (进度: 98%)
2025-07-30 21:32:58 线程3：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-07-30 21:33:27 线程1：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-07-30 21:33:27 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-07-30 21:33:27 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-07-30 21:33:27 [信息] 第一页加载完成，找到验证邮箱按钮
2025-07-30 21:33:27 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-07-30 21:33:27 线程2：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-07-30 21:33:27 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-07-30 21:33:27 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-07-30 21:33:27 [信息] 第一页加载完成，找到验证邮箱按钮
2025-07-30 21:33:29 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-07-30 21:33:29 线程3：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-07-30 21:33:29 线程3：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-07-30 21:33:29 [信息] 第一页相关失败，数据保持不动
2025-07-30 21:33:29 线程3：[错误] 账户注册启动失败: <EMAIL>
2025-07-30 21:33:29 线程3：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-07-30 21:33:30 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-07-30 21:33:30 线程1：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-07-30 21:33:30 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-07-30 21:33:30 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-07-30 21:33:30 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-07-30 21:33:30 线程1：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-07-30 21:33:32 线程1：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-07-30 21:33:32 线程1：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-07-30 21:33:32 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-07-30 21:33:32 线程1：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-07-30 21:33:32 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-07-30 21:33:32 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-07-30 21:33:32 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-07-30 21:33:32 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-07-30 21:33:32 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-07-30 21:33:32 线程1：[信息] 账户注册流程已启动: <EMAIL>
2025-07-30 21:33:32 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-07-30 21:33:32 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-07-30 21:33:32 [信息] [线程1] 等待2秒后开始第一次触发...
2025-07-30 21:33:32 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-07-30 21:33:32 线程2：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 98%)
2025-07-30 21:33:32 [信息] 检测到错误信息，开始重试机制
2025-07-30 21:33:32 线程2：[信息] [信息] 🔄 第1次重试点击验证邮箱按钮... (进度: 98%)
2025-07-30 21:33:32 [信息] 第1次重试点击验证邮箱按钮
2025-07-30 21:33:34 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-07-30 21:33:34 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 21:33:34
2025-07-30 21:33:34 线程2：[信息] [信息] ✅ 第1次重试：已点击验证邮箱按钮 (进度: 100%)
2025-07-30 21:33:34 [信息] 第1次重试：已点击验证邮箱按钮
2025-07-30 21:33:36 线程2：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-07-30 21:33:36 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-07-30 21:33:36 线程2：[信息] [信息] ✅ 第1次重试成功：已到达第二页，继续正常流程 (进度: 100%)
2025-07-30 21:33:36 [信息] 第1次重试成功：已到达第二页
2025-07-30 21:33:36 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-07-30 21:33:36 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-07-30 21:33:36 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-07-30 21:33:36 线程2：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-07-30 21:33:37 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-07-30 21:33:37 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 21:33:37
2025-07-30 21:33:38 线程2：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-07-30 21:33:38 线程2：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-07-30 21:33:39 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-07-30 21:33:39 线程2：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-07-30 21:33:39 线程2：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-07-30 21:33:39 线程2：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-07-30 21:33:39 线程2：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-07-30 21:33:39 线程2：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-07-30 21:33:39 线程2：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-07-30 21:33:39 [信息] [线程2] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-07-30 21:33:39 [信息] [线程2] 等待2秒后开始第一次触发...
2025-07-30 21:33:39 线程2：[信息] 账户注册流程已启动: <EMAIL>
2025-07-30 21:33:39 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-07-30 21:33:40 [信息] [线程1] 第3次触发邮箱验证码获取...（最多20次）
2025-07-30 21:33:40 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 21:33:40
2025-07-30 21:33:41 [信息] [线程2] 第1次触发邮箱验证码获取...（最多20次）
2025-07-30 21:33:41 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-07-30 21:33:41
2025-07-30 21:33:43 [信息] [线程1] 第4次触发邮箱验证码获取...（最多20次）
2025-07-30 21:33:43 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 21:33:43
2025-07-30 21:33:44 [信息] [线程2] 第2次触发邮箱验证码获取...（最多20次）
2025-07-30 21:33:44 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-07-30 21:33:44
2025-07-30 21:33:47 [信息] [线程1] 第5次触发邮箱验证码获取...（最多20次）
2025-07-30 21:33:47 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 21:33:47
2025-07-30 21:33:47 [信息] [线程2] 第3次触发邮箱验证码获取...（最多20次）
2025-07-30 21:33:47 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-07-30 21:33:47
2025-07-30 21:33:50 [信息] [线程1] 第6次触发邮箱验证码获取...（最多20次）
2025-07-30 21:33:50 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 21:33:50
2025-07-30 21:33:50 [信息] [线程2] 第4次触发邮箱验证码获取...（最多20次）
2025-07-30 21:33:50 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-07-30 21:33:50
2025-07-30 21:33:52 线程3：[信息] [信息]  继续注册被调用，当前状态: Error，当前步骤: 1 (进度: 0%)
2025-07-30 21:33:52 线程3：[信息] [信息]  进行智能页面检测... (进度: 0%)
2025-07-30 21:33:52 线程3：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 0%)
2025-07-30 21:33:52 线程3：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 0%)
2025-07-30 21:33:53 [信息] [线程1] 第7次触发邮箱验证码获取...（最多20次）
2025-07-30 21:33:53 [信息] [线程2] 第5次触发邮箱验证码获取...（最多20次）
2025-07-30 21:33:53 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 21:33:53
2025-07-30 21:33:53 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-07-30 21:33:53
2025-07-30 21:33:54 线程3：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 0%)
2025-07-30 21:33:54 线程3：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-07-30 21:33:54 线程3：[信息] [信息]  智能检测到当前在第1页 (进度: 100%)
2025-07-30 21:33:54 线程3：[信息] [信息] 检测当前页面状态... (进度: 100%)
2025-07-30 21:33:54 线程3：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-07-30 21:33:54 线程3：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-07-30 21:33:54 线程3：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 100%)
2025-07-30 21:33:54 线程3：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-07-30 21:33:54 线程3：[信息] [信息] 智能检测到当前在第1页，继续执行... (进度: 100%)
2025-07-30 21:33:54 线程3：[信息] [信息] 🔍 等待第一页加载完成... (进度: 100%)
2025-07-30 21:33:54 线程3：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 100%)
2025-07-30 21:33:54 [信息] 第一页加载完成，找到验证邮箱按钮
2025-07-30 21:33:54 线程3：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 100%)
2025-07-30 21:33:56 [信息] [线程1] 第8次触发邮箱验证码获取...（最多20次）
2025-07-30 21:33:56 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 21:33:56
2025-07-30 21:33:56 [信息] [线程2] 第6次触发邮箱验证码获取...（最多20次）
2025-07-30 21:33:56 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-07-30 21:33:56
2025-07-30 21:33:57 线程3：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 100%)
2025-07-30 21:33:57 线程3：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 100%)
2025-07-30 21:33:57 [信息] 检测到错误信息，开始重试机制
2025-07-30 21:33:57 线程3：[信息] [信息] 🔄 第1次重试点击验证邮箱按钮... (进度: 100%)
2025-07-30 21:33:57 [信息] 第1次重试点击验证邮箱按钮
2025-07-30 21:33:59 [信息] [线程1] 第9次触发邮箱验证码获取...（最多20次）
2025-07-30 21:33:59 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 21:33:59
2025-07-30 21:33:59 [信息] [线程2] 第7次触发邮箱验证码获取...（最多20次）
2025-07-30 21:33:59 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-07-30 21:33:59
2025-07-30 21:33:59 线程3：[信息] [信息] ✅ 第1次重试：已点击验证邮箱按钮 (进度: 100%)
2025-07-30 21:33:59 [信息] 第1次重试：已点击验证邮箱按钮
2025-07-30 21:34:01 线程3：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-07-30 21:34:01 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-07-30 21:34:01 线程3：[信息] [信息] ✅ 第1次重试成功：已到达第二页，继续正常流程 (进度: 100%)
2025-07-30 21:34:01 [信息] 第1次重试成功：已到达第二页
2025-07-30 21:34:01 线程3：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-07-30 21:34:01 线程3：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-07-30 21:34:02 线程3：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-07-30 21:34:02 线程3：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-07-30 21:34:02 [信息] [线程1] 第10次触发邮箱验证码获取...（最多20次）
2025-07-30 21:34:02 [信息] [线程2] 第8次触发邮箱验证码获取...（最多20次）
2025-07-30 21:34:02 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-07-30 21:34:02
2025-07-30 21:34:02 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 21:34:02
2025-07-30 21:34:04 线程3：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-07-30 21:34:04 线程3：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-07-30 21:34:04 线程3：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-07-30 21:34:04 线程3：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-07-30 21:34:04 线程3：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-07-30 21:34:04 线程3：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-07-30 21:34:04 线程3：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-07-30 21:34:04 线程3：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-07-30 21:34:04 线程3：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-07-30 21:34:04 线程3：[信息] 已继续
2025-07-30 21:34:04 [信息] 线程3已继续
2025-07-30 21:34:04 [信息] [线程3] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-07-30 21:34:04 [信息] [线程3] 等待2秒后开始第一次触发...
2025-07-30 21:34:05 [信息] [线程1] 第11次触发邮箱验证码获取...（最多20次）
2025-07-30 21:34:05 [信息] [线程2] 第9次触发邮箱验证码获取...（最多20次）
2025-07-30 21:34:05 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-07-30 21:34:05
2025-07-30 21:34:05 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 21:34:05
2025-07-30 21:34:06 [信息] [线程3] 第1次触发邮箱验证码获取...（最多20次）
2025-07-30 21:34:06 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-07-30 21:34:06
2025-07-30 21:34:08 [信息] [线程1] 第12次触发邮箱验证码获取...（最多20次）
2025-07-30 21:34:08 [信息] [线程2] 第10次触发邮箱验证码获取...（最多20次）
2025-07-30 21:34:08 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 21:34:08
2025-07-30 21:34:08 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-07-30 21:34:08
2025-07-30 21:34:09 [信息] [线程3] 第2次触发邮箱验证码获取...（最多20次）
2025-07-30 21:34:09 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-07-30 21:34:09
2025-07-30 21:34:11 [信息] [线程1] 第13次触发邮箱验证码获取...（最多20次）
2025-07-30 21:34:11 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 21:34:11
2025-07-30 21:34:11 [信息] [线程2] 第11次触发邮箱验证码获取...（最多20次）
2025-07-30 21:34:11 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-07-30 21:34:11
2025-07-30 21:34:12 [信息] [线程3] 第3次触发邮箱验证码获取...（最多20次）
2025-07-30 21:34:12 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-07-30 21:34:12
2025-07-30 21:34:14 [信息] [线程1] 第14次触发邮箱验证码获取...（最多20次）
2025-07-30 21:34:14 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 21:34:14
2025-07-30 21:34:14 [信息] [线程2] 第12次触发邮箱验证码获取...（最多20次）
2025-07-30 21:34:14 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-07-30 21:34:14
2025-07-30 21:34:15 [信息] [线程3] 第4次触发邮箱验证码获取...（最多20次）
2025-07-30 21:34:15 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-07-30 21:34:15
2025-07-30 21:34:16 [信息] [线程1] 邮箱验证码获取成功: 437619，立即停止重复请求
2025-07-30 21:34:16 [信息] [线程1] 已清理请求文件，停止重复触发
2025-07-30 21:34:16 [信息] [线程1] 已清理响应文件
2025-07-30 21:34:16 线程1：[信息] [信息] 验证码获取成功: 437619，正在自动填入... (进度: 25%)
2025-07-30 21:34:16 线程1：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-07-30 21:34:17 线程1：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-07-30 21:34:17 线程1：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-07-30 21:34:18 [信息] [线程2] 第13次触发邮箱验证码获取...（最多20次）
2025-07-30 21:34:18 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-07-30 21:34:18
2025-07-30 21:34:18 [信息] [线程3] 第5次触发邮箱验证码获取...（最多20次）
2025-07-30 21:34:18 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-07-30 21:34:18
2025-07-30 21:34:20 线程1：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-07-30 21:34:20 线程1：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-07-30 21:34:20 线程1：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-07-30 21:34:20 线程1：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-07-30 21:34:20 线程1：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-07-30 21:34:21 [信息] [线程2] 第14次触发邮箱验证码获取...（最多20次）
2025-07-30 21:34:21 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-07-30 21:34:21
2025-07-30 21:34:21 线程1：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-07-30 21:34:21 [信息] [线程3] 第6次触发邮箱验证码获取...（最多20次）
2025-07-30 21:34:21 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-07-30 21:34:21
2025-07-30 21:34:24 [信息] [线程2] 第15次触发邮箱验证码获取...（最多20次）
2025-07-30 21:34:24 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-07-30 21:34:24
2025-07-30 21:34:24 线程1：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-07-30 21:34:24 线程1：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-07-30 21:34:24 线程1：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-07-30 21:34:24 [信息] [线程3] 第7次触发邮箱验证码获取...（最多20次）
2025-07-30 21:34:24 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-07-30 21:34:24
2025-07-30 21:34:26 [信息] [线程2] 邮箱验证码获取成功: 893296，立即停止重复请求
2025-07-30 21:34:26 [信息] [线程2] 已清理请求文件，停止重复触发
2025-07-30 21:34:26 [信息] [线程2] 已清理响应文件
2025-07-30 21:34:26 线程2：[信息] [信息] 验证码获取成功: 893296，正在自动填入... (进度: 25%)
2025-07-30 21:34:26 线程2：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-07-30 21:34:26 线程2：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-07-30 21:34:26 线程2：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-07-30 21:34:27 [信息] [线程3] 第8次触发邮箱验证码获取...（最多20次）
2025-07-30 21:34:27 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-07-30 21:34:27
2025-07-30 21:34:29 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-07-30 21:34:29 线程2：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-07-30 21:34:30 线程2：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-07-30 21:34:30 线程2：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-07-30 21:34:30 线程2：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-07-30 21:34:31 [信息] [线程3] 第9次触发邮箱验证码获取...（最多20次）
2025-07-30 21:34:31 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-07-30 21:34:31
2025-07-30 21:34:31 线程2：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-07-30 21:34:34 [信息] [线程3] 第10次触发邮箱验证码获取...（最多20次）
2025-07-30 21:34:34 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-07-30 21:34:34
2025-07-30 21:34:34 线程2：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-07-30 21:34:34 线程2：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-07-30 21:34:34 线程2：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-07-30 21:34:35 线程1：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-07-30 21:34:35 线程1：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-07-30 21:34:37 [信息] [线程3] 第11次触发邮箱验证码获取...（最多20次）
2025-07-30 21:34:37 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-07-30 21:34:37
2025-07-30 21:34:40 [信息] [线程3] 第12次触发邮箱验证码获取...（最多20次）
2025-07-30 21:34:40 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-07-30 21:34:40
2025-07-30 21:34:43 [信息] [线程3] 第13次触发邮箱验证码获取...（最多20次）
2025-07-30 21:34:43 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-07-30 21:34:43
2025-07-30 21:34:44 线程2：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-07-30 21:34:44 线程2：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-07-30 21:34:46 [信息] [线程3] 第14次触发邮箱验证码获取...（最多20次）
2025-07-30 21:34:46 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-07-30 21:34:46
2025-07-30 21:34:49 [信息] [线程3] 第15次触发邮箱验证码获取...（最多20次）
2025-07-30 21:34:49 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-07-30 21:34:49
2025-07-30 21:34:52 [信息] [线程3] 第16次触发邮箱验证码获取...（最多20次）
2025-07-30 21:34:52 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-07-30 21:34:52
2025-07-30 21:34:56 [信息] [线程3] 第17次触发邮箱验证码获取...（最多20次）
2025-07-30 21:34:56 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-07-30 21:34:56
2025-07-30 21:34:59 线程1：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-07-30 21:34:59 线程1：[信息] [信息] 开始后台获取手机号码，同时填写其他信息... (进度: 38%)
2025-07-30 21:34:59 [信息] [线程3] 第18次触发邮箱验证码获取...（最多20次）
2025-07-30 21:34:59 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-07-30 21:34:59
2025-07-30 21:34:59 线程1：[信息] [信息] 数据国家代码为BR，需要选择Brazil (进度: 38%)
2025-07-30 21:34:59 线程1：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-07-30 21:35:01 线程1：[信息] [信息] 已选择国家: Brazil (进度: 38%)
2025-07-30 21:35:01 线程1：[信息] [信息] 已成功选择国家: Brazil (进度: 38%)
2025-07-30 21:35:01 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-07-30 21:35:01 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-07-30 21:35:02 [信息] [线程3] 第19次触发邮箱验证码获取...（最多20次）
2025-07-30 21:35:02 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-07-30 21:35:02
2025-07-30 21:35:04 线程1：[信息] [信息] 后台获取手机号码成功: +528261062989，已保存到注册数据 (进度: 38%)
2025-07-30 21:35:04 线程1：[信息] [信息] 已选择国家代码 +52 (进度: 38%)
2025-07-30 21:35:04 线程1：[信息] [信息] 等待后台获取的手机号码结果... (进度: 38%)
2025-07-30 21:35:04 线程1：[信息] [信息] 已自动获取并填入手机号码: +528261062989 (进度: 38%)
2025-07-30 21:35:05 [信息] [线程3] 第20次触发邮箱验证码获取...（最多20次）
2025-07-30 21:35:05 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-07-30 21:35:05
2025-07-30 21:35:05 线程1：[信息] [信息] 使用已获取的手机号码: +528261062989（保存本地号码: 8261062989） (进度: 38%)
2025-07-30 21:35:05 线程1：[信息] [信息] 联系信息完成，等待页面加载... (进度: 38%)
2025-07-30 21:35:06 [警告] [线程3] 邮箱验证码获取失败（达到最大重试次数），共尝试20次
2025-07-30 21:35:06 [信息] [线程3] 已清理请求文件
2025-07-30 21:35:06 [信息] [线程3] 已清理响应文件
2025-07-30 21:35:06 线程3：[信息] [信息] 邮箱验证码自动获取失败: 邮箱验证码获取失败（达到最大重试次数），共尝试20次 (进度: 100%)
2025-07-30 21:35:06 线程3：[信息] [信息] 🔴 Microsoft获取验证码失败，转为手动模式 (进度: 100%)
2025-07-30 21:35:07 线程2：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-07-30 21:35:07 线程2：[信息] [信息] 开始后台获取手机号码，同时填写其他信息... (进度: 38%)
2025-07-30 21:35:08 线程2：[信息] [信息] 数据国家代码为BR，需要选择Brazil (进度: 38%)
2025-07-30 21:35:08 线程2：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-07-30 21:35:09 线程1：[信息] [信息] 进入付款信息页面... (进度: 38%)
2025-07-30 21:35:09 线程2：[信息] [信息] 已选择国家: Brazil (进度: 38%)
2025-07-30 21:35:09 线程2：[信息] [信息] 已成功选择国家: Brazil (进度: 38%)
2025-07-30 21:35:09 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-07-30 21:35:10 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-07-30 21:35:10 线程2：[信息] [信息] 后台获取手机号码成功: +526682533605，已保存到注册数据 (进度: 38%)
2025-07-30 21:35:12 线程2：[信息] [信息] 已选择国家代码 +52 (进度: 38%)
2025-07-30 21:35:12 线程2：[信息] [信息] 等待后台获取的手机号码结果... (进度: 38%)
2025-07-30 21:35:12 线程2：[信息] [信息] 已自动获取并填入手机号码: +526682533605 (进度: 38%)
2025-07-30 21:35:13 线程2：[信息] [信息] 使用已获取的手机号码: +526682533605（保存本地号码: 6682533605） (进度: 38%)
2025-07-30 21:35:14 线程2：[信息] [信息] 联系信息完成，等待页面加载... (进度: 38%)
2025-07-30 21:35:17 线程2：[信息] [信息] 进入付款信息页面... (进度: 38%)
2025-07-30 21:35:24 线程3：[信息] [信息]  继续注册被调用，当前状态: WaitingForVerification，当前步骤: 2 (进度: 100%)
2025-07-30 21:35:24 线程3：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-07-30 21:35:24 线程3：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-07-30 21:35:24 线程3：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-07-30 21:35:24 线程3：[信息] [信息] ⚠️ 未找到匹配的页面按钮或链接 (进度: 100%)
2025-07-30 21:35:24 线程3：[信息] [信息] ⚠️ 智能检测失败，启用详细页面分析... (进度: 100%)
2025-07-30 21:35:24 线程3：[信息] [信息] 🔬 执行详细页面分析... (进度: 100%)
2025-07-30 21:35:24 线程3：[信息] [信息] 📄 页面URL: https://signin.aws.amazon.com/signup?request_type=register (进度: 100%)
2025-07-30 21:35:24 线程3：[信息] [信息] 📋 页面标题: AWS Console - Signup (进度: 100%)
2025-07-30 21:35:25 线程3：[信息] [信息] 📊 分析结果: 第三页-密码设置(2/3个元素匹配) (进度: 100%)
2025-07-30 21:35:25 线程3：[信息] [信息]  智能检测到当前在第3页 (进度: 100%)
2025-07-30 21:35:25 线程3：[信息] [信息] 当前状态: WaitingForVerification, 步骤: 3，尝试智能检测页面... (进度: 100%)
2025-07-30 21:35:25 线程3：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-07-30 21:35:25 线程3：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-07-30 21:35:25 线程3：[信息] [信息] ⚠️ 未找到匹配的页面按钮或链接 (进度: 100%)
2025-07-30 21:35:25 线程3：[信息] [信息] ⚠️ 智能检测失败，启用详细页面分析... (进度: 100%)
2025-07-30 21:35:25 线程3：[信息] [信息] 🔬 执行详细页面分析... (进度: 100%)
2025-07-30 21:35:25 线程3：[信息] [信息] 📄 页面URL: https://signin.aws.amazon.com/signup?request_type=register (进度: 100%)
2025-07-30 21:35:25 线程3：[信息] [信息] 📋 页面标题: AWS Console - Signup (进度: 100%)
2025-07-30 21:35:25 线程3：[信息] [信息] 📊 分析结果: 第三页-密码设置(2/3个元素匹配) (进度: 100%)
2025-07-30 21:35:25 线程3：[信息] [信息] 智能检测到当前在第3页，继续执行... (进度: 100%)
2025-07-30 21:35:25 线程3：[信息] [信息] 等待密码设置页面加载... (进度: 100%)
2025-07-30 21:35:25 线程3：[信息] [信息] 开始填写密码信息... (进度: 100%)
2025-07-30 21:35:25 线程3：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 100%)
2025-07-30 21:35:25 线程3：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 100%)
2025-07-30 21:35:25 线程3：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 100%)
2025-07-30 21:35:26 线程3：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 100%)
2025-07-30 21:35:29 线程1：[信息] [信息] 所有自动线程已停止 (进度: 38%)
2025-07-30 21:35:29 线程1：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 38%)
2025-07-30 21:35:29 线程1：[信息] 已暂停
2025-07-30 21:35:29 [信息] 线程1已暂停
2025-07-30 21:35:29 [信息] 线程1已暂停
2025-07-30 21:35:29 线程2：[信息] [信息] 所有自动线程已停止 (进度: 38%)
2025-07-30 21:35:29 线程2：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 38%)
2025-07-30 21:35:29 线程2：[信息] 已暂停
2025-07-30 21:35:29 [信息] 线程2已暂停
2025-07-30 21:35:29 [信息] 线程2已暂停
2025-07-30 21:35:29 线程3：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 100%)
2025-07-30 21:35:29 线程3：[信息] [信息] 等待账户类型确认页面加载... (进度: 100%)
2025-07-30 21:35:29 线程3：[信息] [信息] 开始处理账户类型确认... (进度: 100%)
2025-07-30 21:35:39 线程1：[信息] [信息] 执行第五页失败: 第五页执行失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Textbox, new() { Name = "Credit or Debit card number" })，但手机号码已保存 (进度: 58%)
2025-07-30 21:35:40 线程3：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 100%)
2025-07-30 21:35:40 线程3：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 100%)
2025-07-30 21:35:47 线程2：[信息] [信息] 执行第五页失败: 第五页执行失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Textbox, new() { Name = "Credit or Debit card number" })，但手机号码已保存 (进度: 58%)
2025-07-30 21:36:04 线程3：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 100%)
2025-07-30 21:36:04 线程3：[信息] [信息] 开始后台获取手机号码，同时填写其他信息... (进度: 100%)
2025-07-30 21:36:05 线程3：[信息] [信息] 数据国家代码为BR，需要选择Brazil (进度: 100%)
2025-07-30 21:36:05 线程3：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 100%)
2025-07-30 21:36:06 线程3：[信息] [信息] 后台获取手机号码成功: +525561993907，已保存到注册数据 (进度: 100%)
2025-07-30 21:36:06 线程3：[信息] [信息] 已选择国家: Brazil (进度: 100%)
2025-07-30 21:36:06 线程3：[信息] [信息] 已成功选择国家: Brazil (进度: 100%)
2025-07-30 21:36:06 线程3：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-07-30 21:36:07 线程3：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-07-30 21:36:09 线程3：[信息] [信息] 已选择国家代码 +52 (进度: 100%)
2025-07-30 21:36:09 线程3：[信息] [信息] 等待后台获取的手机号码结果... (进度: 100%)
2025-07-30 21:36:09 线程3：[信息] [信息] 已自动获取并填入手机号码: +525561993907 (进度: 100%)
2025-07-30 21:36:10 线程3：[信息] [信息] 使用已获取的手机号码: +525561993907（保存本地号码: 5561993907） (进度: 100%)
2025-07-30 21:36:11 线程3：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-07-30 21:36:14 线程3：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-07-30 21:36:20 线程1：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 5 (进度: 58%)
2025-07-30 21:36:20 线程1：[信息] [信息]  进行智能页面检测... (进度: 58%)
2025-07-30 21:36:20 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 58%)
2025-07-30 21:36:20 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 58%)
2025-07-30 21:36:20 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Send SMS (step 4 of 5)' → 第6页 (进度: 78%)
2025-07-30 21:36:20 线程1：[信息] [信息] 🔍 疑似第6页，进行二次确认... (进度: 78%)
2025-07-30 21:36:20 线程1：[信息] [信息] ✅ 确认为第6页：找到Mobile phone number输入框 (进度: 100%)
2025-07-30 21:36:20 线程1：[信息] [信息]  智能检测到当前在第6页 (进度: 100%)
2025-07-30 21:36:20 线程1：[信息] [信息] 智能检测到当前在第6页，开始智能处理... (进度: 100%)
2025-07-30 21:36:20 线程1：[信息] [信息] 检测第6页手机验证状态... (进度: 100%)
2025-07-30 21:36:20 线程1：[信息] [信息] [暂停恢复] 恢复API号码: +528261062989 (进度: 100%)
2025-07-30 21:36:21 线程1：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-07-30 21:36:24 线程1：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-07-30 21:36:24 线程1：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-07-30 21:36:29 线程3：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-07-30 21:36:29 线程3：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-07-30 21:36:29 线程3：[信息] 已暂停
2025-07-30 21:36:29 [信息] 线程3已暂停
2025-07-30 21:36:29 [信息] 线程3已暂停
2025-07-30 21:36:30 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34827 字节 (进度: 100%)
2025-07-30 21:36:30 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，34827字节，复杂度符合要求 (进度: 100%)
2025-07-30 21:36:30 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-07-30 21:36:34 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"uyf7pg"},"taskId":"357546ce-6d4a-11f0-8f95-ae68cbd38ae9"} (进度: 100%)
2025-07-30 21:36:34 线程1：[信息] [信息] 第六页第1次识别结果: uyf7pg → 转换为小写: uyf7pg (进度: 100%)
2025-07-30 21:36:34 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-07-30 21:36:34 线程1：[信息] [信息] 第六页已填入验证码: uyf7pg (进度: 100%)
2025-07-30 21:36:34 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-07-30 21:36:38 线程1：[信息] [信息] 第1次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-07-30 21:36:38 线程1：[信息] [信息] 第六页第1次识别异常: 验证码错误 (进度: 100%)
2025-07-30 21:36:40 线程1：[信息] [信息] 第六页第2次尝试自动识别图形验证码... (进度: 100%)
2025-07-30 21:36:44 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 31414 字节 (进度: 100%)
2025-07-30 21:36:44 线程1：[信息] [信息] ✅ 图片验证通过：200x71px，31414字节，复杂度符合要求 (进度: 100%)
2025-07-30 21:36:44 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-07-30 21:36:44 线程3：[信息] [信息] 执行第五页失败: 第五页执行失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Textbox, new() { Name = "Credit or Debit card number" })，但手机号码已保存 (进度: 100%)
2025-07-30 21:36:44 线程3：[信息] 已继续
2025-07-30 21:36:45 [信息] 线程3已继续
2025-07-30 21:36:46 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"dd3ch4"},"taskId":"3cf2eaf0-6d4a-11f0-be4a-ae68cbd38ae9"} (进度: 100%)
2025-07-30 21:36:46 线程1：[信息] [信息] 第六页第2次识别结果: dd3ch4 → 转换为小写: dd3ch4 (进度: 100%)
2025-07-30 21:36:46 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-07-30 21:36:46 线程1：[信息] [信息] 第六页已填入验证码: dd3ch4 (进度: 100%)
2025-07-30 21:36:47 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-07-30 21:36:51 线程1：[信息] [信息] 第2次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-07-30 21:36:51 线程1：[信息] [信息] 第六页第2次识别异常: 验证码错误 (进度: 100%)
2025-07-30 21:36:53 线程1：[信息] [信息] 第六页第3次尝试自动识别图形验证码... (进度: 100%)
2025-07-30 21:36:56 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 31154 字节 (进度: 100%)
2025-07-30 21:36:56 线程1：[信息] [信息] ✅ 图片验证通过：200x71px，31154字节，复杂度符合要求 (进度: 100%)
2025-07-30 21:36:56 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-07-30 21:37:00 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"t3d2q7"},"taskId":"44b7249a-6d4a-11f0-8cf8-261ead5dd06d"} (进度: 100%)
2025-07-30 21:37:00 线程1：[信息] [信息] 第六页第3次识别结果: t3d2q7 → 转换为小写: t3d2q7 (进度: 100%)
2025-07-30 21:37:00 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-07-30 21:37:00 线程1：[信息] [信息] 第六页已填入验证码: t3d2q7 (进度: 100%)
2025-07-30 21:37:00 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-07-30 21:37:04 线程1：[信息] [信息] 第3次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-07-30 21:37:04 线程1：[信息] [信息] 第六页第3次识别异常: 验证码错误 (进度: 100%)
2025-07-30 21:37:04 线程1：[信息] [信息] 第六页图形验证码自动处理失败，转为手动模式 (进度: 100%)
2025-07-30 21:37:04 线程1：[信息] 已继续
2025-07-30 21:37:04 [信息] 线程1已继续
2025-07-30 21:37:21 线程2：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 5 (进度: 58%)
2025-07-30 21:37:21 线程2：[信息] [信息]  进行智能页面检测... (进度: 58%)
2025-07-30 21:37:21 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 58%)
2025-07-30 21:37:21 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 58%)
2025-07-30 21:37:22 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Send SMS (step 4 of 5)' → 第6页 (进度: 78%)
2025-07-30 21:37:22 线程2：[信息] [信息] 🔍 疑似第6页，进行二次确认... (进度: 78%)
2025-07-30 21:37:22 线程2：[信息] [信息] ✅ 确认为第6页：找到Mobile phone number输入框 (进度: 100%)
2025-07-30 21:37:22 线程2：[信息] [信息]  智能检测到当前在第6页 (进度: 100%)
2025-07-30 21:37:22 线程2：[信息] [信息] 智能检测到当前在第6页，开始智能处理... (进度: 100%)
2025-07-30 21:37:22 线程2：[信息] [信息] 检测第6页手机验证状态... (进度: 100%)
2025-07-30 21:37:22 线程2：[信息] [信息] [暂停恢复] 恢复API号码: +526682533605 (进度: 100%)
2025-07-30 21:37:22 线程2：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-07-30 21:37:24 线程1：[信息] [信息]  继续注册被调用，当前状态: WaitingForSMSVerification，当前步骤: 6 (进度: 100%)
2025-07-30 21:37:24 线程1：[信息] [信息] 第六页手动模式继续注册，检测当前页面状态... (进度: 100%)
2025-07-30 21:37:24 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-07-30 21:37:24 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-07-30 21:37:26 线程2：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-07-30 21:37:26 线程2：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-07-30 21:37:28 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Continue (step 4 of 5)' → 第7页 (进度: 100%)
2025-07-30 21:37:28 线程1：[信息] [信息] ✅ 直接确认为第7页 (进度: 100%)
2025-07-30 21:37:28 线程1：[信息] [信息] 检测到已跳转到第七页，更新步骤并执行第七页逻辑 (进度: 100%)
2025-07-30 21:37:28 线程1：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-07-30 21:37:28 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-07-30 21:37:28 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-07-30 21:37:28 线程1：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-07-30 21:37:32 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 4419 字节 (进度: 100%)
2025-07-30 21:37:32 线程2：[信息] [信息] ❌ 图片宽度 301px 不符合验证码特征（应为100-300px） (进度: 100%)
2025-07-30 21:37:32 线程2：[信息] [信息] 第六页捕获的图片不符合验证码特征，跳过此次尝试（不计入重试次数） (进度: 100%)
2025-07-30 21:37:33 线程1：[信息] [信息] 线程1开始独立获取验证码... (进度: 100%)
2025-07-30 21:37:33 线程1：[信息] [信息] 线程1开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-07-30 21:37:33 线程1：[信息] [信息] 线程1第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-07-30 21:37:34 线程2：[信息] [信息] 第六页图片验证失败，第1次图片不符合验证码特征 (进度: 100%)
2025-07-30 21:37:34 线程2：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-07-30 21:37:35 线程1：[信息] [信息] 线程1第1次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-30 21:37:35 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-07-30 21:37:37 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 1686 字节 (进度: 100%)
2025-07-30 21:37:37 线程2：[信息] [信息] ✅ 图片验证通过：201x71px，1686字节，复杂度符合要求 (进度: 100%)
2025-07-30 21:37:37 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-07-30 21:37:41 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":1,"errorCode":"ERROR_UNKNOWN","errorDescription":"未知错误，请联系管理员"} (进度: 100%)
2025-07-30 21:37:41 线程2：[信息] [信息] 第1次识别失败: 未知错误，请联系管理员 (进度: 100%)
2025-07-30 21:37:41 线程2：[信息] [信息] 第六页第1次识别异常: 未知错误，请联系管理员 (进度: 100%)
2025-07-30 21:37:43 线程2：[信息] [信息] 第六页第2次尝试自动识别图形验证码... (进度: 100%)
2025-07-30 21:37:43 线程1：[信息] [信息] 线程1第2次尝试获取验证码...（剩余6次尝试） (进度: 100%)
2025-07-30 21:37:44 线程1：[信息] [信息] 线程1第2次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-30 21:37:44 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-07-30 21:37:46 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 1686 字节 (进度: 100%)
2025-07-30 21:37:46 线程2：[信息] [信息] ✅ 图片验证通过：201x71px，1686字节，复杂度符合要求 (进度: 100%)
2025-07-30 21:37:46 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-07-30 21:37:50 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":1,"errorCode":"ERROR_UNKNOWN","errorDescription":"未知错误，请联系管理员"} (进度: 100%)
2025-07-30 21:37:50 线程2：[信息] [信息] 第2次识别失败: 未知错误，请联系管理员 (进度: 100%)
2025-07-30 21:37:50 线程2：[信息] [信息] 第六页第2次识别异常: 未知错误，请联系管理员 (进度: 100%)
2025-07-30 21:37:52 线程1：[信息] [信息] 线程1第3次尝试获取验证码...（剩余5次尝试） (进度: 100%)
2025-07-30 21:37:52 线程2：[信息] [信息] 第六页第3次尝试自动识别图形验证码... (进度: 100%)
2025-07-30 21:37:52 线程1：[信息] [信息] 线程1第3次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-30 21:37:52 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-07-30 21:37:55 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 1686 字节 (进度: 100%)
2025-07-30 21:37:55 线程2：[信息] [信息] ✅ 图片验证通过：201x71px，1686字节，复杂度符合要求 (进度: 100%)
2025-07-30 21:37:55 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-07-30 21:37:59 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":1,"errorCode":"ERROR_UNKNOWN","errorDescription":"未知错误，请联系管理员"} (进度: 100%)
2025-07-30 21:37:59 线程2：[信息] [信息] 第3次识别失败: 未知错误，请联系管理员 (进度: 100%)
2025-07-30 21:37:59 线程2：[信息] [信息] 第六页第3次识别异常: 未知错误，请联系管理员 (进度: 100%)
2025-07-30 21:37:59 线程2：[信息] [信息] 第六页图形验证码自动处理失败，转为手动模式 (进度: 100%)
2025-07-30 21:37:59 线程2：[信息] 已继续
2025-07-30 21:37:59 [信息] 线程2已继续
2025-07-30 21:38:00 线程1：[信息] [信息] 线程1第4次尝试获取验证码...（剩余4次尝试） (进度: 100%)
2025-07-30 21:38:01 线程1：[信息] [信息] 线程1第4次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-30 21:38:01 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-07-30 21:38:09 线程1：[信息] [信息] 线程1第5次尝试获取验证码...（剩余3次尝试） (进度: 100%)
2025-07-30 21:38:09 线程1：[信息] [信息] 线程1第5次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-30 21:38:09 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-07-30 21:38:17 线程1：[信息] [信息] 线程1第6次尝试获取验证码...（剩余2次尝试） (进度: 100%)
2025-07-30 21:38:17 线程1：[信息] [信息] 线程1第6次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-30 21:38:17 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-07-30 21:38:25 线程1：[信息] [信息] 线程1第7次尝试获取验证码...（剩余1次尝试） (进度: 100%)
2025-07-30 21:38:26 线程1：[信息] [信息] 线程1第7次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-30 21:38:26 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-07-30 21:38:34 线程1：[信息] [信息] 线程1第8次尝试获取验证码...（剩余0次尝试） (进度: 100%)
2025-07-30 21:38:34 线程1：[信息] [信息] 线程1第8次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-30 21:38:34 线程1：[信息] [信息] 线程1验证码获取失败，已尝试8次 (进度: 100%)
2025-07-30 21:38:34 [信息] 线程1手机号码已加入释放队列: +528261062989 (原因: 验证码获取失败)
2025-07-30 21:38:34 线程1：[信息] [信息] 线程1验证码获取失败: 获取失败，已尝试8次 (进度: 100%)
2025-07-30 21:38:34 线程1：[信息] [信息] 验证码获取失败，第1次重试... (进度: 100%)
2025-07-30 21:38:34 线程1：[信息] [信息] 正在自动重试第1次，同时加入黑名单和获取新号码... (进度: 100%)
2025-07-30 21:38:34 线程1：[信息] [信息] 正在返回上一页... (进度: 100%)
2025-07-30 21:38:34 线程1：[信息] [信息] 已点击返回按钮 (进度: 100%)
2025-07-30 21:38:37 线程1：[信息] [信息] 超时号码已加入黑名单 (进度: 100%)
2025-07-30 21:38:37 线程1：[信息] [信息] 正在选择国家代码: mx (进度: 100%)
2025-07-30 21:38:42 线程2：[信息] [信息]  继续注册被调用，当前状态: WaitingForSMSVerification，当前步骤: 6 (进度: 100%)
2025-07-30 21:38:42 线程2：[信息] [信息] 第六页手动模式继续注册，检测当前页面状态... (进度: 100%)
2025-07-30 21:38:42 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-07-30 21:38:42 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-07-30 21:38:42 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Continue (step 4 of 5)' → 第7页 (进度: 100%)
2025-07-30 21:38:42 线程2：[信息] [信息] ✅ 直接确认为第7页 (进度: 100%)
2025-07-30 21:38:42 线程2：[信息] [信息] 检测到已跳转到第七页，更新步骤并执行第七页逻辑 (进度: 100%)
2025-07-30 21:38:42 线程2：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-07-30 21:38:42 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-07-30 21:38:42 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-07-30 21:38:42 线程2：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-07-30 21:38:42 线程1：[信息] [信息] 已打开国家代码下拉列表 (进度: 100%)
2025-07-30 21:38:43 线程1：[信息] [信息] 已选择国家代码: +52 (进度: 100%)
2025-07-30 21:38:43 线程1：[信息] [信息] 后台获取新手机号码... (进度: 100%)
2025-07-30 21:38:45 线程1：[信息] [信息] 后台获取新手机号码成功: +526871774855，已保存到注册数据 (进度: 100%)
2025-07-30 21:38:45 线程1：[信息] [信息] 正在清空并填入新的手机号码... (进度: 100%)
2025-07-30 21:38:45 线程1：[信息] [信息] 已填入新手机号码: +526871774855 (进度: 100%)
2025-07-30 21:38:45 线程1：[信息] [信息] 正在点击发送短信按钮... (进度: 100%)
2025-07-30 21:38:47 线程2：[信息] [信息] 线程2开始独立获取验证码... (进度: 100%)
2025-07-30 21:38:47 线程2：[信息] [信息] 线程2开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-07-30 21:38:47 线程2：[信息] [信息] 线程2第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-07-30 21:38:47 线程1：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-07-30 21:38:47 线程1：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-07-30 21:38:47 线程2：[信息] [信息] 线程2第1次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-30 21:38:47 线程2：[信息] [信息] 线程2等待8秒后进行下一次尝试... (进度: 100%)
2025-07-30 21:38:48 线程1：[信息] [信息] 新手机号码已填入，自动模式：开始处理图形验证码... (进度: 100%)
2025-07-30 21:38:48 线程1：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-07-30 21:38:51 线程1：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-07-30 21:38:51 线程1：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-07-30 21:38:54 线程3：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 5 (进度: 100%)
2025-07-30 21:38:54 线程3：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-07-30 21:38:54 线程3：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-07-30 21:38:54 线程3：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-07-30 21:38:54 线程3：[信息] [信息] 🎯 找到匹配按钮: 'Send SMS (step 4 of 5)' → 第6页 (进度: 100%)
2025-07-30 21:38:54 线程3：[信息] [信息] 🔍 疑似第6页，进行二次确认... (进度: 100%)
2025-07-30 21:38:54 线程3：[信息] [信息] ✅ 确认为第6页：找到Mobile phone number输入框 (进度: 100%)
2025-07-30 21:38:54 线程3：[信息] [信息]  智能检测到当前在第6页 (进度: 100%)
2025-07-30 21:38:54 线程3：[信息] [信息] 智能检测到当前在第6页，开始智能处理... (进度: 100%)
2025-07-30 21:38:54 线程3：[信息] [信息] 检测第6页手机验证状态... (进度: 100%)
2025-07-30 21:38:54 线程3：[信息] [信息] [暂停恢复] 恢复API号码: +525561993907 (进度: 100%)
2025-07-30 21:38:54 线程3：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-07-30 21:38:55 线程2：[信息] [信息] 线程2第2次尝试获取验证码...（剩余6次尝试） (进度: 100%)
2025-07-30 21:38:56 线程2：[信息] [信息] 线程2第2次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-30 21:38:56 线程2：[信息] [信息] 线程2等待8秒后进行下一次尝试... (进度: 100%)
2025-07-30 21:38:56 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-07-30 21:38:56 [信息] 开始释放1个手机号码
2025-07-30 21:38:56 [信息] [手机API] 开始批量释放1个手机号码
2025-07-30 21:38:56 [信息] [手机API] 释放手机号码: +528261062989
2025-07-30 21:38:57 [信息] [手机API] 手机号码释放成功: +528261062989
2025-07-30 21:38:57 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34921 字节 (进度: 100%)
2025-07-30 21:38:57 线程1：[信息] [信息] ✅ 图片验证通过：201x70px，34921字节，复杂度符合要求 (进度: 100%)
2025-07-30 21:38:57 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-07-30 21:38:57 线程3：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-07-30 21:38:57 线程3：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-07-30 21:38:57 [信息] [手机API] 批量释放完成: 成功1个, 失败0个
2025-07-30 21:38:57 [信息] 定时批量释放完成: 批量释放完成: 成功1个, 失败0个
2025-07-30 21:39:01 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"g8w4gc"},"taskId":"8cf21f08-6d4a-11f0-ac35-261ead5dd06d"} (进度: 100%)
2025-07-30 21:39:01 线程1：[信息] [信息] 第六页第1次识别结果: g8w4gc → 转换为小写: g8w4gc (进度: 100%)
2025-07-30 21:39:01 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-07-30 21:39:03 线程1：[信息] [信息] 第六页已填入验证码: g8w4gc (进度: 100%)
2025-07-30 21:39:03 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-07-30 21:39:04 线程2：[信息] [信息] 线程2第3次尝试获取验证码...（剩余5次尝试） (进度: 100%)
2025-07-30 21:39:04 线程2：[信息] [信息] 线程2第3次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-30 21:39:04 线程2：[信息] [信息] 线程2等待8秒后进行下一次尝试... (进度: 100%)
2025-07-30 21:39:06 线程1：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-07-30 21:39:06 线程1：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-07-30 21:39:07 线程3：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34616 字节 (进度: 100%)
2025-07-30 21:39:07 线程3：[信息] [信息] ✅ 图片验证通过：201x71px，34616字节，复杂度符合要求 (进度: 100%)
2025-07-30 21:39:07 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-07-30 21:39:09 线程1：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-07-30 21:39:10 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"x84bsy"},"taskId":"92b566ca-6d4a-11f0-ac35-261ead5dd06d"} (进度: 100%)
2025-07-30 21:39:10 线程3：[信息] [信息] 第六页第1次识别结果: x84bsy → 转换为小写: x84bsy (进度: 100%)
2025-07-30 21:39:10 线程3：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-07-30 21:39:10 线程3：[信息] [信息] 第六页已填入验证码: x84bsy (进度: 100%)
2025-07-30 21:39:10 线程3：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-07-30 21:39:12 线程1：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-07-30 21:39:12 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-07-30 21:39:12 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-07-30 21:39:12 线程1：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-07-30 21:39:12 线程2：[信息] [信息] 线程2第4次尝试获取验证码...（剩余4次尝试） (进度: 100%)
2025-07-30 21:39:13 线程2：[信息] [信息] 线程2第4次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-30 21:39:13 线程2：[信息] [信息] 线程2等待8秒后进行下一次尝试... (进度: 100%)
2025-07-30 21:39:14 线程3：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-07-30 21:39:14 线程3：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-07-30 21:39:17 线程3：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-07-30 21:39:17 线程1：[信息] [信息] 线程1开始独立获取验证码... (进度: 100%)
2025-07-30 21:39:17 线程1：[信息] [信息] 线程1开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-07-30 21:39:17 线程1：[信息] [信息] 线程1第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-07-30 21:39:18 线程1：[信息] [信息] 线程1第1次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-30 21:39:18 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-07-30 21:39:20 线程3：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-07-30 21:39:20 线程3：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-07-30 21:39:20 线程3：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-07-30 21:39:20 线程3：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-07-30 21:39:21 线程2：[信息] [信息] 线程2第5次尝试获取验证码...（剩余3次尝试） (进度: 100%)
2025-07-30 21:39:21 线程2：[信息] [信息] 线程2第5次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-30 21:39:21 线程2：[信息] [信息] 线程2等待8秒后进行下一次尝试... (进度: 100%)
2025-07-30 21:39:25 线程3：[信息] [信息] 线程3开始独立获取验证码... (进度: 100%)
2025-07-30 21:39:25 线程3：[信息] [信息] 线程3开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-07-30 21:39:25 线程3：[信息] [信息] 线程3第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-07-30 21:39:25 线程3：[信息] [信息] 线程3第1次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-30 21:39:25 线程3：[信息] [信息] 线程3等待8秒后进行下一次尝试... (进度: 100%)
2025-07-30 21:39:26 线程1：[信息] [信息] 线程1第2次尝试获取验证码...（剩余6次尝试） (进度: 100%)
2025-07-30 21:39:26 线程1：[信息] [信息] 线程1第2次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-30 21:39:26 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-07-30 21:39:29 线程2：[信息] [信息] 线程2第6次尝试获取验证码...（剩余2次尝试） (进度: 100%)
2025-07-30 21:39:30 线程2：[信息] [信息] 线程2第6次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-30 21:39:30 线程2：[信息] [信息] 线程2等待8秒后进行下一次尝试... (进度: 100%)
2025-07-30 21:39:33 线程3：[信息] [信息] 线程3第2次尝试获取验证码...（剩余6次尝试） (进度: 100%)
2025-07-30 21:39:34 线程3：[信息] [信息] 线程3第2次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-30 21:39:34 线程3：[信息] [信息] 线程3等待8秒后进行下一次尝试... (进度: 100%)
2025-07-30 21:39:34 线程1：[信息] [信息] 线程1第3次尝试获取验证码...（剩余5次尝试） (进度: 100%)
2025-07-30 21:39:35 线程1：[信息] [信息] 线程1第3次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-30 21:39:35 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-07-30 21:39:38 线程2：[信息] [信息] 线程2第7次尝试获取验证码...（剩余1次尝试） (进度: 100%)
2025-07-30 21:39:38 线程2：[信息] [信息] 线程2第7次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-30 21:39:38 线程2：[信息] [信息] 线程2等待8秒后进行下一次尝试... (进度: 100%)
2025-07-30 21:39:42 线程3：[信息] [信息] 线程3第3次尝试获取验证码...（剩余5次尝试） (进度: 100%)
2025-07-30 21:39:42 线程3：[信息] [信息] 线程3第3次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-30 21:39:42 线程3：[信息] [信息] 线程3等待8秒后进行下一次尝试... (进度: 100%)
2025-07-30 21:39:43 线程1：[信息] [信息] 线程1第4次尝试获取验证码...（剩余4次尝试） (进度: 100%)
2025-07-30 21:39:43 线程1：[信息] [信息] 线程1第4次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-30 21:39:43 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-07-30 21:39:46 线程2：[信息] [信息] 线程2第8次尝试获取验证码...（剩余0次尝试） (进度: 100%)
2025-07-30 21:39:46 线程2：[信息] [信息] 线程2第8次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-30 21:39:46 线程2：[信息] [信息] 线程2验证码获取失败，已尝试8次 (进度: 100%)
2025-07-30 21:39:46 [信息] 线程2手机号码已加入释放队列: +526682533605 (原因: 验证码获取失败)
2025-07-30 21:39:46 线程2：[信息] [信息] 线程2验证码获取失败: 获取失败，已尝试8次 (进度: 100%)
2025-07-30 21:39:46 线程2：[信息] [信息] 验证码获取失败，第1次重试... (进度: 100%)
2025-07-30 21:39:46 线程2：[信息] [信息] 正在自动重试第1次，同时加入黑名单和获取新号码... (进度: 100%)
2025-07-30 21:39:46 线程2：[信息] [信息] 正在返回上一页... (进度: 100%)
2025-07-30 21:39:49 线程2：[信息] [信息] 已点击返回按钮 (进度: 100%)
2025-07-30 21:39:49 线程2：[信息] [信息] 超时号码已加入黑名单 (进度: 100%)
2025-07-30 21:39:50 线程3：[信息] [信息] 线程3第4次尝试获取验证码...（剩余4次尝试） (进度: 100%)
2025-07-30 21:39:51 线程3：[信息] [信息] 线程3第4次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-30 21:39:51 线程3：[信息] [信息] 线程3等待8秒后进行下一次尝试... (进度: 100%)
2025-07-30 21:39:51 线程1：[信息] [信息] 线程1第5次尝试获取验证码...（剩余3次尝试） (进度: 100%)
2025-07-30 21:39:52 线程2：[信息] [信息] 正在选择国家代码: mx (进度: 100%)
2025-07-30 21:39:52 线程2：[信息] [信息] 已打开国家代码下拉列表 (进度: 100%)
2025-07-30 21:39:52 线程1：[信息] [信息] 线程1第5次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-30 21:39:52 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-07-30 21:39:53 线程2：[信息] [信息] 已选择国家代码: +52 (进度: 100%)
2025-07-30 21:39:53 线程2：[信息] [信息] 后台获取新手机号码... (进度: 100%)
2025-07-30 21:39:54 线程2：[信息] [信息] 后台获取新手机号码成功: +527353594264，已保存到注册数据 (进度: 100%)
2025-07-30 21:39:54 线程2：[信息] [信息] 正在清空并填入新的手机号码... (进度: 100%)
2025-07-30 21:39:54 线程2：[信息] [信息] 已填入新手机号码: +527353594264 (进度: 100%)
2025-07-30 21:39:54 线程2：[信息] [信息] 正在点击发送短信按钮... (进度: 100%)
2025-07-30 21:39:56 线程2：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-07-30 21:39:56 线程2：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-07-30 21:39:56 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-07-30 21:39:56 [信息] 开始释放1个手机号码
2025-07-30 21:39:56 [信息] [手机API] 开始批量释放1个手机号码
2025-07-30 21:39:56 [信息] [手机API] 释放手机号码: +526682533605
2025-07-30 21:39:57 [信息] [手机API] 手机号码释放成功: +526682533605
2025-07-30 21:39:57 线程2：[信息] [信息] 新手机号码已填入，自动模式：开始处理图形验证码... (进度: 100%)
2025-07-30 21:39:57 线程2：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-07-30 21:39:57 [信息] [手机API] 批量释放完成: 成功1个, 失败0个
2025-07-30 21:39:57 [信息] 定时批量释放完成: 批量释放完成: 成功1个, 失败0个
2025-07-30 21:39:59 线程3：[信息] [信息] 线程3第5次尝试获取验证码...（剩余3次尝试） (进度: 100%)
2025-07-30 21:40:00 线程3：[信息] [信息] 线程3第5次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-30 21:40:00 线程3：[信息] [信息] 线程3等待8秒后进行下一次尝试... (进度: 100%)
2025-07-30 21:40:01 线程2：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-07-30 21:40:01 线程2：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-07-30 21:40:01 线程1：[信息] [信息] 线程1第6次尝试获取验证码...（剩余2次尝试） (进度: 100%)
2025-07-30 21:40:02 线程1：[信息] [信息] 线程1第6次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-30 21:40:02 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-07-30 21:40:05 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 278 字节 (进度: 100%)
2025-07-30 21:40:05 线程2：[信息] [信息] ❌ 图片文件太小 (278 字节)，可能不是验证码 (进度: 100%)
2025-07-30 21:40:05 线程2：[信息] [信息] 第六页捕获的图片不符合验证码特征，跳过此次尝试（不计入重试次数） (进度: 100%)
2025-07-30 21:40:07 线程2：[信息] [信息] 第六页图片验证失败，第1次图片不符合验证码特征 (进度: 100%)
2025-07-30 21:40:07 线程2：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-07-30 21:40:08 线程3：[信息] [信息] 线程3第6次尝试获取验证码...（剩余2次尝试） (进度: 100%)
2025-07-30 21:40:08 线程3：[信息] [信息] 线程3第6次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-30 21:40:08 线程3：[信息] [信息] 线程3等待8秒后进行下一次尝试... (进度: 100%)
2025-07-30 21:40:10 线程1：[信息] [信息] 线程1第7次尝试获取验证码...（剩余1次尝试） (进度: 100%)
2025-07-30 21:40:10 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 278 字节 (进度: 100%)
2025-07-30 21:40:10 线程2：[信息] [信息] ❌ 图片文件太小 (278 字节)，可能不是验证码 (进度: 100%)
2025-07-30 21:40:10 线程2：[信息] [信息] 第六页捕获的图片不符合验证码特征，跳过此次尝试（不计入重试次数） (进度: 100%)
2025-07-30 21:40:10 线程1：[信息] [信息] 线程1第7次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-30 21:40:10 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-07-30 21:40:12 线程2：[信息] [信息] 第六页图片验证失败，第2次图片不符合验证码特征 (进度: 100%)
2025-07-30 21:40:12 线程2：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-07-30 21:40:17 线程3：[信息] [信息] 线程3第7次尝试获取验证码...（剩余1次尝试） (进度: 100%)
2025-07-30 21:40:17 线程3：[信息] [信息] 线程3第7次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-30 21:40:17 线程3：[信息] [信息] 线程3等待8秒后进行下一次尝试... (进度: 100%)
2025-07-30 21:40:17 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 278 字节 (进度: 100%)
2025-07-30 21:40:17 线程2：[信息] [信息] ❌ 图片文件太小 (278 字节)，可能不是验证码 (进度: 100%)
2025-07-30 21:40:17 线程2：[信息] [信息] 第六页捕获的图片不符合验证码特征，跳过此次尝试（不计入重试次数） (进度: 100%)
2025-07-30 21:40:19 线程1：[信息] [信息] 线程1第8次尝试获取验证码...（剩余0次尝试） (进度: 100%)
2025-07-30 21:40:19 线程1：[信息] [信息] 线程1第8次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-30 21:40:19 线程1：[信息] [信息] 线程1验证码获取失败，已尝试8次 (进度: 100%)
2025-07-30 21:40:19 [信息] 线程1手机号码已加入释放队列: +526871774855 (原因: 验证码获取失败)
2025-07-30 21:40:19 线程1：[信息] [信息] 线程1验证码获取失败: 获取失败，已尝试8次 (进度: 100%)
2025-07-30 21:40:19 线程1：[信息] [信息] 验证码获取失败，第2次重试... (进度: 100%)
2025-07-30 21:40:19 线程1：[信息] [信息] 🔴 已重试2次仍无法获取验证码，转为手动模式 (进度: 100%)
2025-07-30 21:40:19 线程1：[信息] 已继续
2025-07-30 21:40:19 [信息] 线程1已继续
2025-07-30 21:40:19 线程2：[信息] [信息] 第六页图片验证失败，第3次图片不符合验证码特征 (进度: 100%)
2025-07-30 21:40:19 线程2：[信息] [信息] 第六页连续3次图片不符合验证码特征，转为手动模式 (进度: 100%)
2025-07-30 21:40:19 线程2：[信息] [信息] 第六页图形验证码自动处理失败，转为手动模式 (进度: 100%)
2025-07-30 21:40:19 线程2：[信息] 已继续
2025-07-30 21:40:19 [信息] 线程2已继续
2025-07-30 21:40:25 线程3：[信息] [信息] 线程3第8次尝试获取验证码...（剩余0次尝试） (进度: 100%)
2025-07-30 21:40:26 线程3：[信息] [信息] 线程3第8次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-07-30 21:40:26 线程3：[信息] [信息] 线程3验证码获取失败，已尝试8次 (进度: 100%)
2025-07-30 21:40:26 [信息] 线程3手机号码已加入释放队列: +525561993907 (原因: 验证码获取失败)
2025-07-30 21:40:26 线程3：[信息] [信息] 线程3验证码获取失败: 获取失败，已尝试8次 (进度: 100%)
2025-07-30 21:40:26 线程3：[信息] [信息] 验证码获取失败，第1次重试... (进度: 100%)
2025-07-30 21:40:26 线程3：[信息] [信息] 正在自动重试第1次，同时加入黑名单和获取新号码... (进度: 100%)
2025-07-30 21:40:26 线程3：[信息] [信息] 正在返回上一页... (进度: 100%)
2025-07-30 21:40:26 线程3：[信息] [信息] 已点击返回按钮 (进度: 100%)
2025-07-30 21:40:26 [信息] 定时检查发现2个待释放手机号码，开始批量释放
2025-07-30 21:40:26 [信息] 开始释放2个手机号码
2025-07-30 21:40:26 [信息] [手机API] 开始批量释放2个手机号码
2025-07-30 21:40:26 [信息] [手机API] 释放手机号码: +526871774855
2025-07-30 21:40:27 [信息] [手机API] 手机号码释放成功: +526871774855
2025-07-30 21:40:27 [信息] [手机API] 释放手机号码: +525561993907
2025-07-30 21:40:28 [信息] [手机API] 手机号码释放成功: +525561993907
2025-07-30 21:40:29 [信息] [手机API] 批量释放完成: 成功2个, 失败0个
2025-07-30 21:40:29 [信息] 定时批量释放完成: 批量释放完成: 成功2个, 失败0个
2025-07-30 21:40:29 线程3：[信息] [信息] 正在选择国家代码: mx (进度: 100%)
2025-07-30 21:40:29 线程3：[信息] [信息] 超时号码已加入黑名单 (进度: 100%)
2025-07-30 21:40:31 线程3：[信息] [信息] 已打开国家代码下拉列表 (进度: 100%)
2025-07-30 21:40:32 线程3：[信息] [信息] 已选择国家代码: +52 (进度: 100%)
2025-07-30 21:40:32 线程3：[信息] [信息] 后台获取新手机号码... (进度: 100%)
2025-07-30 21:40:32 线程3：[信息] [信息] 后台获取新手机号码成功: +526636707533，已保存到注册数据 (进度: 100%)
2025-07-30 21:40:32 线程3：[信息] [信息] 正在清空并填入新的手机号码... (进度: 100%)
2025-07-30 21:40:32 线程3：[信息] [信息] 已填入新手机号码: +526636707533 (进度: 100%)
2025-07-30 21:40:32 线程3：[信息] [信息] 正在点击发送短信按钮... (进度: 100%)
2025-07-30 21:40:34 线程3：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-07-30 21:40:34 线程3：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-07-30 21:40:35 线程3：[信息] [信息] 新手机号码已填入，自动模式：开始处理图形验证码... (进度: 100%)
2025-07-30 21:40:35 线程3：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-07-30 21:40:38 线程3：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-07-30 21:40:38 线程3：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-07-30 21:40:43 线程3：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34986 字节 (进度: 100%)
2025-07-30 21:40:43 线程3：[信息] [信息] ✅ 图片验证通过：201x70px，34986字节，复杂度符合要求 (进度: 100%)
2025-07-30 21:40:43 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-07-30 21:40:47 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"cz4f7g"},"taskId":"cb9c4724-6d4a-11f0-be4a-ae68cbd38ae9"} (进度: 100%)
2025-07-30 21:40:47 线程3：[信息] [信息] 第六页第1次识别结果: cz4f7g → 转换为小写: cz4f7g (进度: 100%)
2025-07-30 21:40:47 线程3：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-07-30 21:40:47 线程3：[信息] [信息] 第六页已填入验证码: cz4f7g (进度: 100%)
2025-07-30 21:40:47 线程3：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-07-30 21:40:51 线程3：[信息] [信息] 第1次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-07-30 21:40:51 线程3：[信息] [信息] 第六页第1次识别异常: 验证码错误 (进度: 100%)
2025-07-30 21:40:53 线程3：[信息] [信息] 第六页第2次尝试自动识别图形验证码... (进度: 100%)
2025-07-30 21:40:56 线程3：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 30827 字节 (进度: 100%)
2025-07-30 21:40:56 线程3：[信息] [信息] ✅ 图片验证通过：200x70px，30827字节，复杂度符合要求 (进度: 100%)
2025-07-30 21:40:56 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-07-30 21:40:57 线程2：[信息] [信息]  继续注册被调用，当前状态: WaitingForSMSVerification，当前步骤: 7 (进度: 100%)
2025-07-30 21:40:57 线程2：[信息] [信息] 第七页手动验证码已输入，检测当前页面状态... (进度: 100%)
2025-07-30 21:40:57 线程2：[信息] [信息] 第七页：智能检测当前页面状态... (进度: 100%)
2025-07-30 21:40:57 线程2：[信息] [信息] 🔧 [DEBUG] 使用修改后的HandleStep7SmartContinue方法 - 版本2025-07-28 (进度: 100%)
2025-07-30 21:41:00 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"y8tp4"},"taskId":"d3672d2a-6d4a-11f0-ac35-261ead5dd06d"} (进度: 100%)
2025-07-30 21:41:00 线程3：[信息] [信息] 第六页第2次识别结果: y8tp4 → 转换为小写: y8tp4 (进度: 100%)
2025-07-30 21:41:00 线程2：[信息] [信息] 检测到验证码已填写: 6981位 (进度: 100%)
2025-07-30 21:41:00 线程2：[信息] [信息] 第七页：检测到用户已输入验证码，直接执行手动模式处理流程... (进度: 100%)
2025-07-30 21:41:00 线程3：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-07-30 21:41:01 线程2：[信息] [信息] 检测到验证码已填写: 6981位 (进度: 100%)
2025-07-30 21:41:01 线程2：[信息] [信息] 第七页：检测到验证码已填写，查找Continue按钮... (进度: 100%)
2025-07-30 21:41:01 线程2：[信息] [信息] 第七页：检测到Continue (step 4 of 5)按钮，自动点击... (进度: 100%)
2025-07-30 21:41:01 线程3：[信息] [信息] 第六页已填入验证码: y8tp4 (进度: 100%)
2025-07-30 21:41:02 线程2：[信息] [信息] 第七页：已点击Continue (step 4 of 5)按钮，等待页面跳转... (进度: 100%)
2025-07-30 21:41:02 线程3：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-07-30 21:41:05 线程2：[信息] [信息] 第七页Continue按钮处理完成，开始执行后续注册流程... (进度: 100%)
2025-07-30 21:41:05 线程2：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-07-30 21:41:05 线程2：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-07-30 21:41:05 线程2：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-07-30 21:41:05 线程2：[信息] 已暂停
2025-07-30 21:41:05 [信息] 线程2已暂停
2025-07-30 21:41:05 [信息] 线程2已暂停
2025-07-30 21:41:06 线程3：[信息] [信息] 第2次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-07-30 21:41:06 线程3：[信息] [信息] 第六页第2次识别异常: 验证码错误 (进度: 100%)
2025-07-30 21:41:06 线程2：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 7 (进度: 100%)
2025-07-30 21:41:06 线程2：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-07-30 21:41:06 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-07-30 21:41:06 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-07-30 21:41:06 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Continue (step 4 of 5)' → 第7页 (进度: 100%)
2025-07-30 21:41:06 线程2：[信息] [信息] ✅ 直接确认为第7页 (进度: 100%)
2025-07-30 21:41:06 线程2：[信息] [信息]  智能检测到当前在第7页 (进度: 100%)
2025-07-30 21:41:06 线程2：[信息] [信息] 智能检测到当前在第7页，开始智能处理... (进度: 100%)
2025-07-30 21:41:06 线程2：[信息] 已继续
2025-07-30 21:41:06 [信息] 线程2已继续
2025-07-30 21:41:06 线程2：[信息] [信息] 自动模式验证完成，API手机号码已加入黑名单: 加入黑名单成功 (进度: 100%)
2025-07-30 21:41:08 线程3：[信息] [信息] 第六页第3次尝试自动识别图形验证码... (进度: 100%)
2025-07-30 21:41:11 线程3：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 31550 字节 (进度: 100%)
2025-07-30 21:41:11 线程3：[信息] [信息] ✅ 图片验证通过：200x70px，31550字节，复杂度符合要求 (进度: 100%)
2025-07-30 21:41:11 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-07-30 21:41:12 线程2：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-07-30 21:41:12 线程2：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-07-30 21:41:12 线程2：[信息] 已暂停
2025-07-30 21:41:12 [信息] 线程2已暂停
2025-07-30 21:41:12 [信息] 线程2已暂停
2025-07-30 21:41:13 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"xf6t8f"},"taskId":"dbe90298-6d4a-11f0-9ce8-e28f2cb08a64"} (进度: 100%)
2025-07-30 21:41:13 线程3：[信息] [信息] 第六页第3次识别结果: xf6t8f → 转换为小写: xf6t8f (进度: 100%)
2025-07-30 21:41:13 线程3：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-07-30 21:41:13 线程3：[信息] [信息] 第六页已填入验证码: xf6t8f (进度: 100%)
2025-07-30 21:41:13 线程3：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-07-30 21:41:17 线程3：[信息] [信息] 第3次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-07-30 21:41:17 线程3：[信息] [信息] 第六页第3次识别异常: 验证码错误 (进度: 100%)
2025-07-30 21:41:17 线程3：[信息] [信息] 第六页图形验证码自动处理失败，转为手动模式 (进度: 100%)
2025-07-30 21:41:17 线程3：[信息] 已继续
2025-07-30 21:41:17 [信息] 线程3已继续
2025-07-30 21:41:35 线程2：[信息] [信息] 点击完成注册按钮失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Button, new() { Name = "Complete sign up" }) (进度: 100%)
2025-07-30 21:41:35 线程2：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-07-30 21:41:38 线程2：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-07-30 21:41:38 线程2：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-07-30 21:41:43 线程2：[信息] [信息] ❌ 控制台按钮处理失败: Timeout 5000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Link, new() { Name = "Go to the AWS Management Console" }) to be visible (进度: 100%)
2025-07-30 21:41:43 [信息] 控制台按钮处理失败: Timeout 5000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Link, new() { Name = "Go to the AWS Management Console" }) to be visible
2025-07-30 21:41:43 线程2：[信息] [信息] ⚠️ 密钥提取失败，按原本逻辑完成注册 (进度: 100%)
2025-07-30 21:41:43 [信息] 密钥提取失败，按原本逻辑完成注册
2025-07-30 21:41:43 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：74NJ35Js5 ③AWS密码：8oKqdPpW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息： (进度: 100%)
2025-07-30 21:41:43 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：74NJ35Js5 ③AWS密码：8oKqdPpW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-07-30 21:41:43 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：74NJ35Js5 ③AWS密码：8oKqdPpW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-07-30 21:41:43 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：74NJ35Js5 ③AWS密码：8oKqdPpW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-07-30 21:41:43 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：74NJ35Js5 ③AWS密码：8oKqdPpW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-07-30 21:41:43 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：74NJ35Js5 ③AWS密码：8oKqdPpW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-07-30 21:41:43 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-07-30 21:41:43 线程2：[信息] 账户注册完成(无密钥)，等待后续操作: <EMAIL>
2025-07-30 21:41:43 线程2：[信息] [信息] ✅ 注册成功（密钥提取失败） (进度: 98%)
2025-07-30 21:41:43 [信息] 注册完成（密钥提取失败）
2025-07-30 21:41:43 线程2：[信息] 已继续
2025-07-30 21:41:43 [信息] 线程2已继续
2025-07-30 21:41:43 [信息] 开始处理数据完成事件: <EMAIL>
2025-07-30 21:41:43 [信息] 已将完成数据移动到注册成功区域: <EMAIL>
2025-07-30 21:41:43 [信息] 已完成数据移除: <EMAIL>
2025-07-30 21:41:43 [信息] 数据完成事件处理完毕: <EMAIL>
2025-07-30 21:41:43 线程2：[信息] 最终完成: 注册完成，无法提取密钥: <EMAIL> (类型: WithoutKeys)
2025-07-30 21:41:43 [信息] 线程2数据完成事件已通知主窗口并最终完成: <EMAIL>
2025-07-30 21:42:06 线程3：[信息] [信息]  继续注册被调用，当前状态: WaitingForSMSVerification，当前步骤: 7 (进度: 100%)
2025-07-30 21:42:06 线程3：[信息] [信息] 第七页手动验证码已输入，检测当前页面状态... (进度: 100%)
2025-07-30 21:42:06 线程3：[信息] [信息] 第七页：智能检测当前页面状态... (进度: 100%)
2025-07-30 21:42:06 线程3：[信息] [信息] 🔧 [DEBUG] 使用修改后的HandleStep7SmartContinue方法 - 版本2025-07-28 (进度: 100%)
2025-07-30 21:42:07 线程3：[信息] [信息] 检测到验证码已填写: 6981位 (进度: 100%)
2025-07-30 21:42:07 线程3：[信息] [信息] 第七页：检测到用户已输入验证码，直接执行手动模式处理流程... (进度: 100%)
2025-07-30 21:42:07 线程3：[信息] [信息] 检测到验证码已填写: 6981位 (进度: 100%)
2025-07-30 21:42:07 线程3：[信息] [信息] 第七页：检测到验证码已填写，查找Continue按钮... (进度: 100%)
2025-07-30 21:42:07 线程3：[信息] [信息] 第七页：检测到Continue (step 4 of 5)按钮，自动点击... (进度: 100%)
2025-07-30 21:42:07 线程3：[信息] [信息] 第七页：已点击Continue (step 4 of 5)按钮，等待页面跳转... (进度: 100%)
2025-07-30 21:42:10 线程3：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-07-30 21:42:10 线程3：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-07-30 21:42:10 线程3：[信息] 已暂停
2025-07-30 21:42:10 [信息] 线程3已暂停
2025-07-30 21:42:10 [信息] 线程3已暂停
2025-07-30 21:42:10 线程3：[信息] [信息] 第七页Continue按钮处理完成，开始执行后续注册流程... (进度: 100%)
2025-07-30 21:42:10 线程3：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-07-30 21:42:12 线程3：[信息] [信息] 自动模式验证完成，API手机号码已加入黑名单: 加入黑名单成功 (进度: 100%)
2025-07-30 21:42:20 线程3：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-07-30 21:42:20 线程3：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-07-30 21:42:20 线程3：[信息] 已暂停
2025-07-30 21:42:20 [信息] 线程3已暂停
2025-07-30 21:42:20 [信息] 线程3已暂停
2025-07-30 21:42:40 线程3：[信息] [信息] 点击完成注册按钮失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Button, new() { Name = "Complete sign up" }) (进度: 100%)
2025-07-30 21:42:40 线程3：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-07-30 21:42:43 线程3：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-07-30 21:42:43 线程3：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-07-30 21:42:46 线程3：[信息] [信息] ❌ 控制台按钮处理失败: Target page, context or browser has been closed
Call log:
  - waiting for GetByRole(AriaRole.Link, new() { Name = "Go to the AWS Management Console" }) to be visible (进度: 100%)
2025-07-30 21:42:46 [信息] 控制台按钮处理失败: Target page, context or browser has been closed
Call log:
  - waiting for GetByRole(AriaRole.Link, new() { Name = "Go to the AWS Management Console" }) to be visible
2025-07-30 21:42:46 线程3：[信息] [信息] ⚠️ 密钥提取失败，按原本逻辑完成注册 (进度: 100%)
2025-07-30 21:42:46 [信息] 密钥提取失败，按原本逻辑完成注册
2025-07-30 21:42:46 线程3：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：93xt3MQ1Z26l ③AWS密码：EPVjVp6v ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息： (进度: 100%)
2025-07-30 21:42:46 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：93xt3MQ1Z26l ③AWS密码：EPVjVp6v ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-07-30 21:42:46 线程3：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：93xt3MQ1Z26l ③AWS密码：EPVjVp6v ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-07-30 21:42:46 [信息] 线程3请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：93xt3MQ1Z26l ③AWS密码：EPVjVp6v ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-07-30 21:42:46 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：93xt3MQ1Z26l ③AWS密码：EPVjVp6v ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-07-30 21:42:46 [信息] 线程3剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：93xt3MQ1Z26l ③AWS密码：EPVjVp6v ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-07-30 21:42:46 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-07-30 21:42:46 线程3：[信息] 账户注册完成(无密钥)，等待后续操作: <EMAIL>
2025-07-30 21:42:46 线程3：[信息] [信息] ✅ 注册成功（密钥提取失败） (进度: 98%)
2025-07-30 21:42:46 [信息] 注册完成（密钥提取失败）
2025-07-30 21:42:46 [信息] 开始处理数据完成事件: <EMAIL>
2025-07-30 21:42:46 [信息] 已将完成数据移动到注册成功区域: <EMAIL>
2025-07-30 21:42:46 [信息] 已完成数据移除: <EMAIL>
2025-07-30 21:42:46 [信息] 数据完成事件处理完毕: <EMAIL>
2025-07-30 21:42:46 线程3：[信息] 最终完成: 注册完成，无法提取密钥: <EMAIL> (类型: WithoutKeys)
2025-07-30 21:42:46 [信息] 线程3数据完成事件已通知主窗口并最终完成: <EMAIL>
2025-07-30 21:42:46 线程3：[信息] 已继续
2025-07-30 21:42:46 [信息] 线程3已继续
2025-07-30 21:44:34 线程1：[信息] [信息]  继续注册被调用，当前状态: WaitingForSMSVerification，当前步骤: 7 (进度: 100%)
2025-07-30 21:44:34 线程1：[信息] [信息] 第七页手动验证码已输入，检测当前页面状态... (进度: 100%)
2025-07-30 21:44:34 线程1：[信息] [信息] 第七页：智能检测当前页面状态... (进度: 100%)
2025-07-30 21:44:34 线程1：[信息] [信息] 🔧 [DEBUG] 使用修改后的HandleStep7SmartContinue方法 - 版本2025-07-28 (进度: 100%)
2025-07-30 21:44:35 线程1：[信息] [信息] 第七页：检测到验证码未输入，根据配置决定自动获取还是手动输入... (进度: 100%)
2025-07-30 21:44:35 线程1：[信息] [信息] 第七页：检测到手机验证码自动模式，开始自动获取验证码流程... (进度: 100%)
2025-07-30 21:44:35 线程1：[信息] [信息] 使用已有的完整手机号码: +526871774855 (进度: 100%)
2025-07-30 21:44:35 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-07-30 21:44:35 线程1：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-07-30 21:44:37 线程1：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-07-30 21:44:37 线程1：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-07-30 21:44:37 线程1：[信息] 已暂停
2025-07-30 21:44:37 [信息] 线程1已暂停
2025-07-30 21:44:37 [信息] 线程1已暂停
2025-07-30 21:44:38 线程1：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 7 (进度: 100%)
2025-07-30 21:44:38 线程1：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-07-30 21:44:38 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-07-30 21:44:38 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-07-30 21:44:38 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Complete sign up' → 第8页 (进度: 100%)
2025-07-30 21:44:38 线程1：[信息] [信息] ✅ 直接确认为第8页 (进度: 100%)
2025-07-30 21:44:38 线程1：[信息] [信息]  智能检测到当前在第8页 (进度: 100%)
2025-07-30 21:44:38 线程1：[信息] [信息] 智能检测到当前在第8页，开始智能处理... (进度: 100%)
2025-07-30 21:44:38 线程1：[信息] 已继续
2025-07-30 21:44:38 [信息] 线程1已继续
2025-07-30 21:44:40 线程1：[信息] [信息] 线程1开始独立获取验证码... (进度: 100%)
2025-07-30 21:44:40 线程1：[信息] [信息] 线程1开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-07-30 21:44:40 线程1：[信息] [信息] 线程1第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-07-30 21:44:42 线程1：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-07-30 21:44:42 线程1：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-07-30 21:44:42 线程1：[信息] 已暂停
2025-07-30 21:44:42 [信息] 线程1已暂停
2025-07-30 21:44:42 [信息] 线程1已暂停
2025-07-30 21:44:42 线程1：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 8 (进度: 100%)
2025-07-30 21:44:42 线程1：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-07-30 21:44:42 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-07-30 21:44:42 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-07-30 21:44:42 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Complete sign up' → 第8页 (进度: 100%)
2025-07-30 21:44:42 线程1：[信息] [信息] ✅ 直接确认为第8页 (进度: 100%)
2025-07-30 21:44:42 线程1：[信息] [信息]  智能检测到当前在第8页 (进度: 100%)
2025-07-30 21:44:42 线程1：[信息] [信息] 智能检测到当前在第8页，开始智能处理... (进度: 100%)
2025-07-30 21:44:42 线程1：[信息] 已继续
2025-07-30 21:44:42 [信息] 线程1已继续
2025-07-30 21:44:42 线程1：[信息] [信息] 线程1第1次尝试失败: 获取验证码失败: 验证码获取失败，请查询数据列表，或联系管理员 (进度: 100%)
2025-07-30 21:44:42 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-07-30 21:44:47 线程1：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-07-30 21:44:47 线程1：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-07-30 21:44:47 线程1：[信息] 已暂停
2025-07-30 21:44:47 [信息] 线程1已暂停
2025-07-30 21:44:47 [信息] 线程1已暂停
2025-07-30 21:44:48 线程1：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 8 (进度: 100%)
2025-07-30 21:44:48 线程1：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-07-30 21:44:48 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-07-30 21:44:48 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-07-30 21:44:49 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Complete sign up' → 第8页 (进度: 100%)
2025-07-30 21:44:49 线程1：[信息] [信息] ✅ 直接确认为第8页 (进度: 100%)
2025-07-30 21:44:49 线程1：[信息] [信息]  智能检测到当前在第8页 (进度: 100%)
2025-07-30 21:44:49 线程1：[信息] [信息] 智能检测到当前在第8页，开始智能处理... (进度: 100%)
2025-07-30 21:44:49 线程1：[信息] 已继续
2025-07-30 21:44:49 [信息] 线程1已继续
2025-07-30 21:44:50 线程1：[信息] [信息] 线程1第2次尝试获取验证码...（剩余6次尝试） (进度: 100%)
2025-07-30 21:44:51 线程1：[信息] [信息] 线程1第2次尝试失败: 获取验证码失败: 验证码获取失败，请查询数据列表，或联系管理员 (进度: 100%)
2025-07-30 21:44:51 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-07-30 21:44:59 线程1：[信息] [信息] 线程1第3次尝试获取验证码...（剩余5次尝试） (进度: 100%)
2025-07-30 21:44:59 线程1：[信息] [信息] 线程1第3次尝试失败: 获取验证码失败: 验证码获取失败，请查询数据列表，或联系管理员 (进度: 100%)
2025-07-30 21:44:59 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-07-30 21:45:07 线程1：[信息] [信息] 线程1第4次尝试获取验证码...（剩余4次尝试） (进度: 100%)
2025-07-30 21:45:08 线程1：[信息] [信息] 线程1第4次尝试失败: 获取验证码失败: 验证码获取失败，请查询数据列表，或联系管理员 (进度: 100%)
2025-07-30 21:45:08 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-07-30 21:45:12 线程1：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-07-30 21:45:12 线程1：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-07-30 21:45:12 线程1：[信息] 已暂停
2025-07-30 21:45:12 [信息] 线程1已暂停
2025-07-30 21:45:12 [信息] 线程1已暂停
2025-07-30 21:45:16 线程1：[信息] [信息] 线程1第5次尝试获取验证码...（剩余3次尝试） (进度: 100%)
2025-07-30 21:45:16 线程1：[信息] [信息] 线程1第5次尝试失败: 获取验证码失败: 验证码获取失败，请查询数据列表，或联系管理员 (进度: 100%)
2025-07-30 21:45:16 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-07-30 21:45:24 线程1：[信息] [信息] 线程1第6次尝试获取验证码...（剩余2次尝试） (进度: 100%)
2025-07-30 21:45:25 线程1：[信息] [信息] 线程1第6次尝试失败: 获取验证码失败: 验证码获取失败，请查询数据列表，或联系管理员 (进度: 100%)
2025-07-30 21:45:25 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-07-30 21:45:29 [信息] 获取线程1当前数据: <EMAIL>
2025-07-30 21:45:29 线程1：[信息] 终止时正在处理的数据: <EMAIL>
2025-07-30 21:45:29 线程1：[信息] 数据详情: <EMAIL>|aLIlx2iz|Isaac Bill|Ultrapar|Rua H-108 111|Aparecida de Goiania|GO|74937-070|5552700148961671|04|28|247|Isaac Bill|10r6w36Y58U|BR
2025-07-30 21:45:29 线程1：[信息] 多线程终止 - 复制密钥信息到剪贴板
2025-07-30 21:45:29 线程1：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：10r6w36Y58U ③AWS密码：aLIlx2iz ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-07-30 21:45:29 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：10r6w36Y58U ③AWS密码：aLIlx2iz ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 21:45:29 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：10r6w36Y58U ③AWS密码：aLIlx2iz ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 21:45:29 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：10r6w36Y58U ③AWS密码：aLIlx2iz ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 21:45:29 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：10r6w36Y58U ③AWS密码：aLIlx2iz ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 21:45:29 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：10r6w36Y58U ③AWS密码：aLIlx2iz ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 21:45:29 [信息] 所有线程已完成，通知主窗口重置状态
2025-07-30 21:45:29 [信息] 多线程状态已重置
2025-07-30 21:45:29 [信息] 所有线程已完成，通知主窗口重置状态
2025-07-30 21:45:29 [信息] 多线程状态已重置
2025-07-30 21:45:29 线程1：[信息] [信息] 注册已终止 (进度: 100%)
2025-07-30 21:45:29 [信息] 所有线程已完成，通知主窗口重置状态
2025-07-30 21:45:29 [信息] 多线程状态已重置
2025-07-30 21:45:29 线程1：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-07-30 21:45:29 [信息] 所有线程已完成，通知主窗口重置状态
2025-07-30 21:45:29 [信息] 多线程状态已重置
2025-07-30 21:45:29 线程1：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：10r6w36Y58U ③AWS密码：aLIlx2iz ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-07-30 21:45:29 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：10r6w36Y58U ③AWS密码：aLIlx2iz ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 21:45:29 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：10r6w36Y58U ③AWS密码：aLIlx2iz ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 21:45:29 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：10r6w36Y58U ③AWS密码：aLIlx2iz ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 21:45:29 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：10r6w36Y58U ③AWS密码：aLIlx2iz ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 21:45:29 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：10r6w36Y58U ③AWS密码：aLIlx2iz ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 21:45:29 [信息] 所有线程已完成，通知主窗口重置状态
2025-07-30 21:45:29 [信息] 多线程状态已重置
2025-07-30 21:45:29 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-07-30 21:45:29 线程1：[信息] 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\AWS_Thread_1_20250730_213243
2025-07-30 21:45:29 [信息] 所有线程已完成，通知主窗口重置状态
2025-07-30 21:45:29 [信息] 多线程状态已重置
2025-07-30 21:45:29 线程1：[信息] 已终止
2025-07-30 21:45:29 [信息] 线程1已终止
2025-07-30 21:45:29 [信息] 开始处理线程1终止数据，共1个数据
2025-07-30 21:45:29 [信息] 处理线程1终止数据: <EMAIL>
2025-07-30 21:45:29 [信息] 从注册数据列表中移除: <EMAIL>
2025-07-30 21:45:29 [信息] 线程1终止 - 数据已移动到终止列表: <EMAIL>
2025-07-30 21:45:29 [信息] 线程1终止数据处理完成，成功移动1个数据
2025-07-30 21:45:29 [信息] UniformGrid列数已更新为: 1
2025-07-30 21:45:29 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-07-30 21:45:29 [信息] 线程1已终止
2025-07-30 21:45:33 线程1：[信息] [信息] 线程1第7次尝试获取验证码...（剩余1次尝试） (进度: 0%)
2025-07-30 21:45:34 线程1：[信息] [信息] 线程1第7次尝试失败: 获取验证码失败: 验证码获取失败，请查询数据列表，或联系管理员 (进度: 0%)
2025-07-30 21:45:34 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 0%)
2025-07-30 21:45:42 线程1：[信息] [信息] 线程1第8次尝试获取验证码...（剩余0次尝试） (进度: 0%)
2025-07-30 21:45:42 线程1：[信息] [信息] 线程1第8次尝试失败: 获取验证码失败: 验证码获取失败，请查询数据列表，或联系管理员 (进度: 0%)
2025-07-30 21:45:42 线程1：[信息] [信息] 线程1验证码获取失败，已尝试8次 (进度: 0%)
2025-07-30 21:45:42 [信息] 线程1手机号码已加入释放队列: +526871774855 (原因: 验证码获取失败)
2025-07-30 21:45:42 线程1：[信息] [信息] 线程1验证码获取失败: 获取失败，已尝试8次 (进度: 0%)
2025-07-30 21:45:42 线程1：[信息] [信息] 检测到注册已终止或暂停，停止超时处理 (进度: 0%)
2025-07-30 21:45:42 线程1：[信息] 已继续
2025-07-30 21:45:42 [信息] 线程1已继续
2025-07-30 21:45:56 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-07-30 21:45:56 [信息] 开始释放1个手机号码
2025-07-30 21:45:56 [信息] [手机API] 开始批量释放1个手机号码
2025-07-30 21:45:56 [信息] [手机API] 释放手机号码: +526871774855
2025-07-30 21:45:57 [信息] [手机API] 手机号码释放成功: +526871774855
2025-07-30 21:45:58 [信息] [手机API] 批量释放完成: 成功1个, 失败0个
2025-07-30 21:45:58 [信息] 定时批量释放完成: 批量释放完成: 成功1个, 失败0个
2025-07-30 21:47:41 [信息] 多线程窗口引用已清理
2025-07-30 21:47:41 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-07-30 21:47:41 [信息] 多线程管理窗口正在关闭
2025-07-30 21:47:46 [信息] 线程数量已选择: 2
2025-07-30 21:47:48 [按钮操作] 开始注册 -> 启动注册流程
2025-07-30 21:47:48 [信息] 开始启动多线程注册，线程数量: 2
2025-07-30 21:47:48 [信息] 多线程管理器已重新创建
2025-07-30 21:47:48 [信息] 开始启动多线程注册，线程数量: 2，数据条数: 5
2025-07-30 21:47:48 [信息] 所有线程已停止并清理
2025-07-30 21:47:48 [信息] 正在初始化多线程服务...
2025-07-30 21:47:48 [信息] 手机号码管理器已初始化，将在第一个线程完成第二页后获取手机号码
2025-07-30 21:47:48 [信息] 多线程服务初始化完成
2025-07-30 21:47:48 [信息] 数据分配完成：共5条数据分配给2个线程
2025-07-30 21:47:48 [信息] 线程1分配到3条数据
2025-07-30 21:47:48 [信息] 线程2分配到2条数据
2025-07-30 21:47:48 [信息] 屏幕工作区域: 1280x672
2025-07-30 21:47:48 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x310), 列1行1, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-07-30 21:47:48 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-07-30 21:47:48 线程1：[信息] 添加数据到队列: <EMAIL>
2025-07-30 21:47:48 线程1：[信息] 添加数据到队列: <EMAIL>
2025-07-30 21:47:48 线程1：[信息] 添加数据到队列: <EMAIL>
2025-07-30 21:47:48 [信息] 线程1已创建，窗口位置: (0, 0)
2025-07-30 21:47:48 [信息] 屏幕工作区域: 1280x672
2025-07-30 21:47:48 [信息] 线程2窗口布局: 位置(0, 329), 大小(384x310), 列1行2, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-07-30 21:47:48 线程2：[信息] 已创建，窗口位置: (0, 329)
2025-07-30 21:47:48 线程2：[信息] 添加数据到队列: <EMAIL>
2025-07-30 21:47:48 线程2：[信息] 添加数据到队列: <EMAIL>
2025-07-30 21:47:48 [信息] 线程2已创建，窗口位置: (0, 329)
2025-07-30 21:47:48 [信息] 多线程注册启动成功，共2个线程
2025-07-30 21:47:48 线程1：[信息] 开始启动注册流程
2025-07-30 21:47:48 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-30 21:47:48 线程1：[信息] 启动无痕Chrome浏览器...
2025-07-30 21:47:48 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-07-30 21:47:48 [信息] 多线程管理窗口已初始化
2025-07-30 21:47:48 [信息] UniformGrid列数已更新为: 1
2025-07-30 21:47:48 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-07-30 21:47:48 [信息] 多线程管理窗口已打开
2025-07-30 21:47:48 [信息] 多线程注册启动成功，共2个线程
2025-07-30 21:47:49 [信息] UniformGrid列数已更新为: 1
2025-07-30 21:47:49 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-07-30 21:47:49 线程2：[信息] 开始启动注册流程
2025-07-30 21:47:49 线程2：[信息] 开始启动浏览器: 位置(0, 329), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-07-30 21:47:49 线程2：[信息] 启动无痕Chrome浏览器...
2025-07-30 21:47:49 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-07-30 21:47:53 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0
2025-07-30 21:47:53 [信息] UniformGrid列数已更新为: 1
2025-07-30 21:47:53 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-07-30 21:47:53 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-07-30 21:47:53 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0
2025-07-30 21:47:53 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-07-30 21:47:55 线程1：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-07-30 21:47:55 线程2：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-07-30 21:47:58 线程1：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-07-30 21:47:58 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 0%)
2025-07-30 21:47:58 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-07-30 21:47:58 线程1：[信息] 浏览器启动成功
2025-07-30 21:47:58 线程1：[信息] 获取下一个数据: <EMAIL>
2025-07-30 21:47:58 线程1：[信息] 开始处理账户: <EMAIL>
2025-07-30 21:47:58 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-07-30 21:47:58 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-07-30 21:47:58 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-07-30 21:47:58 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-07-30 21:47:58 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-07-30 21:47:58 线程2：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-07-30 21:48:00 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 0%)
2025-07-30 21:48:00 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-07-30 21:48:00 线程2：[信息] 浏览器启动成功
2025-07-30 21:48:00 线程2：[信息] 获取下一个数据: <EMAIL>
2025-07-30 21:48:00 线程2：[信息] 开始处理账户: <EMAIL>
2025-07-30 21:48:00 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-07-30 21:48:00 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-07-30 21:48:00 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-07-30 21:48:00 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-07-30 21:48:00 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-07-30 21:48:00 线程1：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-07-30 21:48:00 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-07-30 21:48:00 线程2：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-07-30 21:48:00 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-07-30 21:48:00 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-07-30 21:48:00 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-07-30 21:48:00 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-07-30 21:48:00 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-07-30 21:48:00 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-07-30 21:48:00 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-07-30 21:48:23 线程1：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-07-30 21:48:23 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-07-30 21:48:23 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-07-30 21:48:23 [信息] 第一页加载完成，找到验证邮箱按钮
2025-07-30 21:48:23 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-07-30 21:48:26 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-07-30 21:48:26 线程1：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-07-30 21:48:26 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-07-30 21:48:26 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-07-30 21:48:26 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-07-30 21:48:26 线程1：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-07-30 21:48:28 线程1：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-07-30 21:48:28 线程1：[信息] [信息] 🔍 第2次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-07-30 21:48:28 线程1：[信息] [信息] ✅ 第2次检测发现图形验证码！ (进度: 100%)
2025-07-30 21:48:28 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-07-30 21:48:28 线程1：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-07-30 21:48:28 线程1：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-07-30 21:48:28 线程1：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-07-30 21:48:30 线程2：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-07-30 21:48:30 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-07-30 21:48:30 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-07-30 21:48:30 [信息] 第一页加载完成，找到验证邮箱按钮
2025-07-30 21:48:30 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-07-30 21:48:33 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-07-30 21:48:33 线程2：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-07-30 21:48:33 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-07-30 21:48:33 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-07-30 21:48:33 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-07-30 21:48:33 线程2：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-07-30 21:48:35 线程2：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-07-30 21:48:35 线程2：[信息] [信息] 🔍 第2次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-07-30 21:48:35 线程2：[信息] [信息] ✅ 第2次检测发现图形验证码！ (进度: 100%)
2025-07-30 21:48:35 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-07-30 21:48:35 线程2：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-07-30 21:48:35 线程2：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-07-30 21:48:35 线程2：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-07-30 21:48:37 线程1：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 34830 字节 (进度: 100%)
2025-07-30 21:48:37 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，34830字节，复杂度符合要求 (进度: 100%)
2025-07-30 21:48:37 线程1：[信息] [信息] 正在调用云打码API识别验证码... (进度: 100%)
2025-07-30 21:48:43 线程2：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35275 字节 (进度: 100%)
2025-07-30 21:48:43 线程2：[信息] [信息] ✅ 图片验证通过：201x71px，35275字节，复杂度符合要求 (进度: 100%)
2025-07-30 21:48:43 线程2：[信息] [信息] 正在调用云打码API识别验证码... (进度: 100%)
2025-07-30 21:48:52 线程2：[信息] [信息] 🔍 API响应内容: {"msg":"识别成功","code":10000,"data":{"code":0,"data":"8h7ccb","time":6.2726850509643555,"externel":2,"file_path":"https:\/\/ali-jfb2024.oss-cn-chengdu.aliyuncs.com\/jfb_upload\/dabiao\/2025\/07\/1303703f7c7863bb06ff2a0289ba8dfb.png","order_unique_id":"1303703f7c7863bb06ff2a0289ba8dfb","reduce_score":16,"unique_code":"1303703f7c7863bb06ff2a0289ba8dfb"}} (进度: 100%)
2025-07-30 21:48:52 线程2：[信息] [信息] 第一页第1次识别结果: 8h7ccb → 转换为小写: 8h7ccb (进度: 100%)
2025-07-30 21:48:52 线程2：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-07-30 21:48:52 线程2：[信息] [信息] 已填入验证码: 8h7ccb (进度: 100%)
2025-07-30 21:48:52 线程2：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-07-30 21:48:54 线程2：[信息] [信息] 第一页第1次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-07-30 21:48:54 线程2：[信息] [信息] 第一页第2次失败，等待新验证码... (进度: 100%)
2025-07-30 21:48:56 线程2：[信息] [信息] 第一页第2次尝试自动识别图形验证码... (进度: 100%)
2025-07-30 21:48:58 线程1：[信息] [信息] 🔍 API响应内容: {"msg":"识别成功","code":10000,"data":{"code":0,"data":"mbz84h","time":16.294695377349854,"externel":2,"file_path":"https:\/\/ali-jfb2024.oss-cn-chengdu.aliyuncs.com\/jfb_upload\/dabiao\/2025\/07\/73682258718005cd23f233e1f84f5648.png","order_unique_id":"73682258718005cd23f233e1f84f5648","reduce_score":16,"unique_code":"73682258718005cd23f233e1f84f5648"}} (进度: 100%)
2025-07-30 21:48:58 线程1：[信息] [信息] 第一页第1次识别结果: mbz84h → 转换为小写: mbz84h (进度: 100%)
2025-07-30 21:48:58 线程1：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-07-30 21:48:58 线程1：[信息] [信息] 已填入验证码: mbz84h (进度: 100%)
2025-07-30 21:48:58 线程1：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-07-30 21:48:59 线程2：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 31589 字节 (进度: 100%)
2025-07-30 21:48:59 线程2：[信息] [信息] ✅ 图片验证通过：200x71px，31589字节，复杂度符合要求 (进度: 100%)
2025-07-30 21:48:59 线程2：[信息] [信息] 正在调用云打码API识别验证码... (进度: 100%)
2025-07-30 21:49:03 线程1：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-07-30 21:49:03 线程1：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-07-30 21:49:03 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-07-30 21:49:03 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-07-30 21:49:03 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-07-30 21:49:03 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-07-30 21:49:03 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-07-30 21:49:03 线程1：[信息] 账户注册流程已启动: <EMAIL>
2025-07-30 21:49:03 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-07-30 21:49:03 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-07-30 21:49:03 [信息] [线程1] 等待2秒后开始第一次触发...
2025-07-30 21:49:05 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-07-30 21:49:05 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 21:49:05
2025-07-30 21:49:08 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-07-30 21:49:08 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 21:49:08
2025-07-30 21:49:11 线程2：[信息] [信息] 🔍 API响应内容: {"msg":"识别成功","code":10000,"data":{"code":0,"data":"xczyb7","time":11.255013704299927,"externel":2,"file_path":"https:\/\/ali-jfb2024.oss-cn-chengdu.aliyuncs.com\/jfb_upload\/dabiao\/2025\/07\/97f7a4a74efb648dd83794b7ec0c75f8.png","order_unique_id":"97f7a4a74efb648dd83794b7ec0c75f8","reduce_score":16,"unique_code":"97f7a4a74efb648dd83794b7ec0c75f8"}} (进度: 100%)
2025-07-30 21:49:11 线程2：[信息] [信息] 第一页第2次识别结果: xczyb7 → 转换为小写: xczyb7 (进度: 100%)
2025-07-30 21:49:11 线程2：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-07-30 21:49:11 [信息] [线程1] 第3次触发邮箱验证码获取...（最多20次）
2025-07-30 21:49:11 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 21:49:11
2025-07-30 21:49:11 线程2：[信息] [信息] 已填入验证码: xczyb7 (进度: 100%)
2025-07-30 21:49:11 线程2：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-07-30 21:49:13 线程2：[信息] [信息] 第一页第2次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-07-30 21:49:13 线程2：[信息] [信息] 第一页第3次失败，等待新验证码... (进度: 100%)
2025-07-30 21:49:14 [信息] [线程1] 第4次触发邮箱验证码获取...（最多20次）
2025-07-30 21:49:14 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 21:49:14
2025-07-30 21:49:15 线程2：[信息] [信息] 第一页第3次尝试自动识别图形验证码... (进度: 100%)
2025-07-30 21:49:17 [信息] [线程1] 第5次触发邮箱验证码获取...（最多20次）
2025-07-30 21:49:17 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 21:49:17
2025-07-30 21:49:20 [信息] [线程1] 第6次触发邮箱验证码获取...（最多20次）
2025-07-30 21:49:20 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 21:49:20
2025-07-30 21:49:23 [信息] [线程1] 第7次触发邮箱验证码获取...（最多20次）
2025-07-30 21:49:23 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 21:49:23
2025-07-30 21:49:25 [信息] [线程1] 邮箱验证码获取成功: 563316，立即停止重复请求
2025-07-30 21:49:25 [信息] [线程1] 已清理请求文件，停止重复触发
2025-07-30 21:49:25 [信息] [线程1] 已清理响应文件
2025-07-30 21:49:25 线程1：[信息] [信息] 验证码获取成功: 563316，正在自动填入... (进度: 25%)
2025-07-30 21:49:25 线程1：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-07-30 21:49:25 线程1：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-07-30 21:49:25 线程1：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-07-30 21:49:28 线程1：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-07-30 21:49:28 线程1：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-07-30 21:49:28 线程1：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-07-30 21:49:28 线程2：[信息] [信息] 第一页验证码元素等待超时，转为手动模式 (进度: 100%)
2025-07-30 21:49:28 线程2：[信息] [信息] 第一页验证码元素等待超时，直接转为手动模式 (进度: 100%)
2025-07-30 21:49:28 线程2：[信息] [信息] 第一页图形验证码自动处理失败，转为手动模式 (进度: 100%)
2025-07-30 21:49:28 线程1：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-07-30 21:49:28 线程1：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-07-30 21:49:29 线程2：[信息] [信息] 检测到第一页验证码已完成，继续流程 (进度: 100%)
2025-07-30 21:49:29 线程2：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-07-30 21:49:29 线程2：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-07-30 21:49:29 线程2：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-07-30 21:49:29 线程2：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-07-30 21:49:29 [信息] [线程2] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-07-30 21:49:29 [信息] [线程2] 等待2秒后开始第一次触发...
2025-07-30 21:49:29 线程2：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-07-30 21:49:29 线程2：[信息] 账户注册流程已启动: <EMAIL>
2025-07-30 21:49:29 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-07-30 21:49:29 线程1：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-07-30 21:49:31 [信息] [线程2] 第1次触发邮箱验证码获取...（最多20次）
2025-07-30 21:49:31 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-07-30 21:49:31
2025-07-30 21:49:32 线程1：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-07-30 21:49:32 线程1：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-07-30 21:49:32 线程1：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-07-30 21:49:34 [信息] [线程2] 第2次触发邮箱验证码获取...（最多20次）
2025-07-30 21:49:34 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-07-30 21:49:34
2025-07-30 21:49:37 [信息] [线程2] 第3次触发邮箱验证码获取...（最多20次）
2025-07-30 21:49:37 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-07-30 21:49:37
2025-07-30 21:49:39 [信息] [线程2] 邮箱验证码获取成功: 689108，立即停止重复请求
2025-07-30 21:49:39 [信息] [线程2] 已清理请求文件，停止重复触发
2025-07-30 21:49:39 [信息] [线程2] 已清理响应文件
2025-07-30 21:49:39 线程2：[信息] [信息] 验证码获取成功: 689108，正在自动填入... (进度: 25%)
2025-07-30 21:49:39 线程2：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-07-30 21:49:39 线程2：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-07-30 21:49:39 线程2：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-07-30 21:49:42 线程1：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-07-30 21:49:42 线程1：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-07-30 21:49:42 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-07-30 21:49:42 线程2：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-07-30 21:49:42 线程2：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-07-30 21:49:42 线程2：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-07-30 21:49:42 线程2：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-07-30 21:49:43 线程2：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-07-30 21:49:46 线程2：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-07-30 21:49:46 线程2：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-07-30 21:49:46 线程2：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-07-30 21:50:06 线程2：[信息] [信息] 第3.5页执行失败: Timeout 15000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Button, new() { Name = "Choose paid plan" }) to be visible (进度: 38%)
2025-07-30 21:50:07 线程1：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-07-30 21:50:07 线程1：[信息] [信息] 开始后台获取手机号码，同时填写其他信息... (进度: 38%)
2025-07-30 21:50:08 线程1：[信息] [信息] 数据国家代码为BR，需要选择Brazil (进度: 38%)
2025-07-30 21:50:08 线程1：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-07-30 21:50:09 线程1：[信息] [信息] 已选择国家: Brazil (进度: 38%)
2025-07-30 21:50:09 线程1：[信息] [信息] 已成功选择国家: Brazil (进度: 38%)
2025-07-30 21:50:09 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-07-30 21:50:10 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-07-30 21:50:10 线程1：[信息] [信息] 后台获取手机号码成功: +526671609880，已保存到注册数据 (进度: 38%)
2025-07-30 21:50:12 线程1：[信息] [信息] 已选择国家代码 +52 (进度: 38%)
2025-07-30 21:50:13 线程1：[信息] [信息] 等待后台获取的手机号码结果... (进度: 38%)
2025-07-30 21:50:13 线程1：[信息] [信息] 已自动获取并填入手机号码: +526671609880 (进度: 38%)
2025-07-30 21:50:14 线程1：[信息] [信息] 使用已获取的手机号码: +526671609880（保存本地号码: 6671609880） (进度: 38%)
2025-07-30 21:50:14 线程1：[信息] [信息] 联系信息完成，等待页面加载... (进度: 38%)
2025-07-30 21:50:17 线程1：[信息] [信息] 进入付款信息页面... (进度: 38%)
2025-07-30 21:50:30 线程2：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 3 (进度: 38%)
2025-07-30 21:50:30 线程2：[信息] [信息]  进行智能页面检测... (进度: 38%)
2025-07-30 21:50:30 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 38%)
2025-07-30 21:50:30 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 38%)
2025-07-30 21:50:30 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Choose paid plan' → 第3页 (进度: 38%)
2025-07-30 21:50:30 线程2：[信息] [信息] 检测到第3.5页（账户类型确认页面），直接点击Choose paid plan按钮... (进度: 38%)
2025-07-30 21:50:30 线程2：[信息] [信息] 账户类型确认完成，进入第4页（联系信息页面）... (进度: 38%)
2025-07-30 21:50:32 线程1：[信息] [信息] 所有自动线程已停止 (进度: 38%)
2025-07-30 21:50:32 线程1：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 38%)
2025-07-30 21:50:32 线程1：[信息] 已暂停
2025-07-30 21:50:32 [信息] 线程1已暂停
2025-07-30 21:50:32 [信息] 线程1已暂停
2025-07-30 21:50:33 线程2：[信息] [信息] 检测到注册已暂停或终止，停止后续操作 (进度: 38%)
2025-07-30 21:50:33 线程2：[信息] [信息] ⚠️ 智能检测失败，启用详细页面分析... (进度: 38%)
2025-07-30 21:50:33 线程2：[信息] [信息] 🔬 执行详细页面分析... (进度: 38%)
2025-07-30 21:50:33 线程2：[信息] [信息] ❌ 详细分析失败: Execution context was destroyed, most likely because of a navigation (进度: 38%)
2025-07-30 21:50:33 线程2：[信息] [信息] 智能检测到当前在第3页，开始智能处理... (进度: 38%)
2025-07-30 21:50:39 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-07-30 21:50:47 线程1：[信息] [信息] 执行第五页失败: 第五页执行失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Textbox, new() { Name = "Credit or Debit card number" })，但手机号码已保存 (进度: 58%)
2025-07-30 21:50:58 线程2：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-07-30 21:51:08 线程2：[信息] [信息] 第三页执行失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("input[name*='password']") to be visible (进度: 38%)
2025-07-30 21:51:08 线程2：[信息] 已继续
2025-07-30 21:51:08 [信息] 线程2已继续
2025-07-30 21:52:20 线程2：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 3 (进度: 38%)
2025-07-30 21:52:20 线程2：[信息] [信息]  进行智能页面检测... (进度: 38%)
2025-07-30 21:52:20 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 38%)
2025-07-30 21:52:20 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 38%)
2025-07-30 21:52:20 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Agree and Continue (step 2 of 5)' → 第4页 (进度: 38%)
2025-07-30 21:52:20 线程2：[信息] [信息] ✅ 直接确认为第4页 (进度: 100%)
2025-07-30 21:52:20 线程2：[信息] [信息]  智能检测到当前在第4页 (进度: 100%)
2025-07-30 21:52:20 线程2：[信息] [信息] 智能检测到当前在第4页，开始智能处理... (进度: 100%)
2025-07-30 21:52:20 线程2：[信息] [信息] 开始后台获取手机号码，同时填写其他信息... (进度: 100%)
2025-07-30 21:52:20 线程2：[信息] [信息] 数据国家代码为BR，需要选择Brazil (进度: 100%)
2025-07-30 21:52:21 线程2：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 100%)
2025-07-30 21:52:22 线程2：[信息] [信息] 后台获取手机号码成功: +526462605971，已保存到注册数据 (进度: 100%)
2025-07-30 21:52:22 线程2：[信息] [信息] 已选择国家: Brazil (进度: 100%)
2025-07-30 21:52:22 线程2：[信息] [信息] 已成功选择国家: Brazil (进度: 100%)
2025-07-30 21:52:22 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-07-30 21:52:22 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-07-30 21:52:25 线程2：[信息] [信息] 已选择国家代码 +52 (进度: 100%)
2025-07-30 21:52:25 线程2：[信息] [信息] 等待后台获取的手机号码结果... (进度: 100%)
2025-07-30 21:52:25 线程2：[信息] [信息] 已自动获取并填入手机号码: +526462605971 (进度: 100%)
2025-07-30 21:52:26 线程2：[信息] [信息] 使用已获取的手机号码: +526462605971（保存本地号码: 6462605971） (进度: 100%)
2025-07-30 21:52:26 线程2：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-07-30 21:52:29 线程2：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-07-30 21:52:53 线程2：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-07-30 21:52:53 线程2：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-07-30 21:52:53 线程2：[信息] 已暂停
2025-07-30 21:52:53 [信息] 线程2已暂停
2025-07-30 21:52:53 [信息] 线程2已暂停
2025-07-30 21:52:59 线程2：[信息] [信息] 执行第五页失败: 第五页执行失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Textbox, new() { Name = "Credit or Debit card number" })，但手机号码已保存 (进度: 100%)
2025-07-30 21:52:59 线程2：[信息] 已继续
2025-07-30 21:52:59 [信息] 线程2已继续
2025-07-30 21:57:48 [按钮操作] 终止所有线程 -> 终止所有正在进行的注册流程
2025-07-30 21:57:48 [信息] 开始检查2个线程的终止状态
2025-07-30 21:57:48 [信息] 线程1注册未完成（状态: Paused），将被终止
2025-07-30 21:57:48 [信息] 获取线程1当前数据: <EMAIL>
2025-07-30 21:57:48 [信息] 线程1正在处理数据: <EMAIL>
2025-07-30 21:57:48 [信息] 线程2注册未完成（状态: Paused），将被终止
2025-07-30 21:57:48 [信息] 获取线程2当前数据: <EMAIL>
2025-07-30 21:57:48 [信息] 线程2正在处理数据: <EMAIL>
2025-07-30 21:57:48 [信息] 总共找到2个需要终止的数据: <EMAIL>, <EMAIL>
2025-07-30 21:57:48 [错误] 无法获取主窗口引用，数据移动失败
2025-07-30 21:57:48 线程1：[信息] 终止时正在处理的数据: <EMAIL>
2025-07-30 21:57:48 线程1：[信息] 数据详情: <EMAIL>|2AId4cr5|James Shafer|Localiza|Rua Arthur Sander Ferreira 10|Araucria|PR|83704-430|5552700148021328|03|28|942|James Shafer|2B9i415yd|BR
2025-07-30 21:57:48 线程1：[信息] 多线程终止 - 复制密钥信息到剪贴板
2025-07-30 21:57:48 线程2：[信息] 终止时正在处理的数据: <EMAIL>
2025-07-30 21:57:48 线程2：[信息] 数据详情: <EMAIL>|Y1xW3ixh|James Shafer|Suzano|Rua Arthur Sander Ferreira 10|Araucria|PR|83704-430|5552700149442564|04|28|829|James Shafer|x8CA69LjKT|BR
2025-07-30 21:57:48 线程2：[信息] 多线程终止 - 复制密钥信息到剪贴板
2025-07-30 21:57:49 [信息] 多线程窗口引用已清理
2025-07-30 21:57:49 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-07-30 21:57:49 [信息] 多线程管理窗口正在关闭
2025-07-30 21:57:49 线程1：[信息] [信息] STA线程复制失败: OpenClipboard 失败 (0x800401D0 (CLIPBRD_E_CANT_OPEN)) (进度: 58%)
2025-07-30 21:57:49 线程1：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：2B9i415yd ③AWS密码：2AId4cr5 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 98%)
2025-07-30 21:57:49 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：2B9i415yd ③AWS密码：2AId4cr5 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 21:57:49 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：2B9i415yd ③AWS密码：2AId4cr5 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 21:57:49 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：2B9i415yd ③AWS密码：2AId4cr5 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 21:57:49 线程1：[信息] [信息] 注册已终止 (进度: 98%)
2025-07-30 21:57:49 线程1：[信息] [信息] 所有自动线程已停止 (进度: 98%)
2025-07-30 21:57:49 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：x8CA69LjKT ③AWS密码：Y1xW3ixh ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-07-30 21:57:49 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：x8CA69LjKT ③AWS密码：Y1xW3ixh ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 21:57:49 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：x8CA69LjKT ③AWS密码：Y1xW3ixh ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 21:57:49 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：x8CA69LjKT ③AWS密码：Y1xW3ixh ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 21:57:49 线程2：[信息] [信息] 注册已终止 (进度: 100%)
2025-07-30 21:57:49 线程2：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-07-30 21:57:50 线程1：[信息] [信息] STA线程复制失败: OpenClipboard 失败 (0x800401D0 (CLIPBRD_E_CANT_OPEN)) (进度: 98%)
2025-07-30 21:57:50 线程1：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：2B9i415yd ③AWS密码：2AId4cr5 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 98%)
2025-07-30 21:57:50 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：2B9i415yd ③AWS密码：2AId4cr5 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 21:57:50 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：2B9i415yd ③AWS密码：2AId4cr5 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 21:57:50 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：2B9i415yd ③AWS密码：2AId4cr5 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 21:57:50 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-07-30 21:57:50 线程1：[信息] 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\AWS_Thread_1_20250730_214748
2025-07-30 21:57:50 线程1：[信息] 已终止
2025-07-30 21:57:50 [信息] 线程1已终止
2025-07-30 21:57:50 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：x8CA69LjKT ③AWS密码：Y1xW3ixh ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-07-30 21:57:50 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：x8CA69LjKT ③AWS密码：Y1xW3ixh ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 21:57:50 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：x8CA69LjKT ③AWS密码：Y1xW3ixh ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 21:57:50 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：x8CA69LjKT ③AWS密码：Y1xW3ixh ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 21:57:50 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-07-30 21:57:50 线程2：[信息] 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\AWS_Thread_2_20250730_214749
2025-07-30 21:57:50 线程2：[信息] 已终止
2025-07-30 21:57:50 [信息] 线程2已终止
2025-07-30 22:00:40 [信息] AWS自动注册工具启动
2025-07-30 22:00:40 [信息] 程序版本: 1.0.0.0
2025-07-30 22:00:40 [信息] 启动时间: 2025-07-30 22:00:40
2025-07-30 22:00:40 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-07-30 22:00:40 [信息] 线程数量已选择: 1
2025-07-30 22:00:40 [信息] 线程数量选择初始化完成
2025-07-30 22:00:40 [信息] 程序初始化完成
2025-07-30 22:00:41 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-07-30 22:00:44 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-07-30-巴西.txt
2025-07-30 22:00:44 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-07-30-巴西.txt
2025-07-30 22:00:45 [信息] 成功加载 3 条数据
2025-07-30 22:00:46 [信息] 线程数量已选择: 2
2025-07-30 22:00:50 [按钮操作] 开始注册 -> 启动注册流程
2025-07-30 22:00:50 [信息] 开始启动多线程注册，线程数量: 2
2025-07-30 22:00:50 [信息] 开始启动多线程注册，线程数量: 2，数据条数: 3
2025-07-30 22:00:50 [信息] 所有线程已停止并清理
2025-07-30 22:00:50 [信息] 正在初始化多线程服务...
2025-07-30 22:00:50 [信息] 手机号码管理器已初始化，将在第一个线程完成第二页后获取手机号码
2025-07-30 22:00:50 [信息] 多线程服务初始化完成
2025-07-30 22:00:50 [信息] 数据分配完成：共3条数据分配给2个线程
2025-07-30 22:00:50 [信息] 线程1分配到2条数据
2025-07-30 22:00:50 [信息] 线程2分配到1条数据
2025-07-30 22:00:50 [信息] 屏幕工作区域: 1280x672
2025-07-30 22:00:50 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x310), 列1行1, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-07-30 22:00:50 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-07-30 22:00:50 线程1：[信息] 添加数据到队列: <EMAIL>
2025-07-30 22:00:50 线程1：[信息] 添加数据到队列: <EMAIL>
2025-07-30 22:00:50 [信息] 线程1已创建，窗口位置: (0, 0)
2025-07-30 22:00:50 [信息] 屏幕工作区域: 1280x672
2025-07-30 22:00:50 [信息] 线程2窗口布局: 位置(0, 329), 大小(384x310), 列1行2, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-07-30 22:00:50 线程2：[信息] 已创建，窗口位置: (0, 329)
2025-07-30 22:00:50 线程2：[信息] 添加数据到队列: <EMAIL>
2025-07-30 22:00:50 [信息] 线程2已创建，窗口位置: (0, 329)
2025-07-30 22:00:50 [信息] 多线程注册启动成功，共2个线程
2025-07-30 22:00:50 线程1：[信息] 开始启动注册流程
2025-07-30 22:00:50 线程2：[信息] 开始启动注册流程
2025-07-30 22:00:50 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-07-30 22:00:50 线程2：[信息] 开始启动浏览器: 位置(0, 329), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-07-30 22:00:50 线程2：[信息] 启动无痕Chrome浏览器...
2025-07-30 22:00:50 线程1：[信息] 启动无痕Chrome浏览器...
2025-07-30 22:00:50 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-07-30 22:00:50 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-07-30 22:00:50 [信息] 多线程管理窗口已初始化
2025-07-30 22:00:50 [信息] UniformGrid列数已更新为: 1
2025-07-30 22:00:50 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-07-30 22:00:50 [信息] 多线程管理窗口已打开
2025-07-30 22:00:50 [信息] 多线程注册启动成功，共2个线程
2025-07-30 22:00:52 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0
2025-07-30 22:00:52 [信息] UniformGrid列数已更新为: 1
2025-07-30 22:00:52 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-07-30 22:00:52 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-07-30 22:00:52 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0
2025-07-30 22:00:52 [信息] UniformGrid列数已更新为: 1
2025-07-30 22:00:52 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-07-30 22:00:52 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-07-30 22:00:54 线程1：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-07-30 22:00:54 线程2：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-07-30 22:00:57 线程1：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-07-30 22:00:59 线程2：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-07-30 22:00:59 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 0%)
2025-07-30 22:00:59 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-07-30 22:00:59 线程1：[信息] 浏览器启动成功
2025-07-30 22:00:59 线程1：[信息] 获取下一个数据: <EMAIL>
2025-07-30 22:00:59 线程1：[信息] 开始处理账户: <EMAIL>
2025-07-30 22:00:59 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-07-30 22:00:59 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-07-30 22:00:59 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-07-30 22:00:59 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-07-30 22:00:59 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-07-30 22:00:59 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 0%)
2025-07-30 22:00:59 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-07-30 22:00:59 线程2：[信息] 浏览器启动成功
2025-07-30 22:00:59 线程2：[信息] 获取下一个数据: <EMAIL>
2025-07-30 22:00:59 线程2：[信息] 开始处理账户: <EMAIL>
2025-07-30 22:00:59 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-07-30 22:00:59 线程1：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-07-30 22:00:59 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-07-30 22:00:59 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-07-30 22:00:59 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-07-30 22:00:59 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-07-30 22:00:59 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-07-30 22:00:59 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-07-30 22:00:59 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-07-30 22:00:59 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-07-30 22:00:59 线程2：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-07-30 22:00:59 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-07-30 22:00:59 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-07-30 22:00:59 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-07-30 22:00:59 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-07-30 22:01:21 线程1：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-07-30 22:01:21 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-07-30 22:01:21 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-07-30 22:01:21 [信息] 第一页加载完成，找到验证邮箱按钮
2025-07-30 22:01:21 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-07-30 22:01:23 线程2：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-07-30 22:01:23 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-07-30 22:01:23 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-07-30 22:01:23 [信息] 第一页加载完成，找到验证邮箱按钮
2025-07-30 22:01:23 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-07-30 22:01:24 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-07-30 22:01:24 线程1：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-07-30 22:01:24 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-07-30 22:01:24 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-07-30 22:01:25 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-07-30 22:01:25 线程1：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-07-30 22:01:26 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-07-30 22:01:26 线程2：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-07-30 22:01:26 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-07-30 22:01:26 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-07-30 22:01:26 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-07-30 22:01:26 线程2：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-07-30 22:01:27 线程1：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-07-30 22:01:27 线程1：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-07-30 22:01:27 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-07-30 22:01:27 线程1：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-07-30 22:01:27 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-07-30 22:01:27 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-07-30 22:01:27 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-07-30 22:01:27 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-07-30 22:01:27 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-07-30 22:01:27 线程1：[信息] 账户注册流程已启动: <EMAIL>
2025-07-30 22:01:27 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-07-30 22:01:27 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-07-30 22:01:27 [信息] [线程1] 等待2秒后开始第一次触发...
2025-07-30 22:01:28 线程2：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-07-30 22:01:28 线程2：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-07-30 22:01:28 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-07-30 22:01:28 线程2：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-07-30 22:01:28 线程2：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-07-30 22:01:28 线程2：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-07-30 22:01:28 线程2：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-07-30 22:01:28 线程2：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-07-30 22:01:28 线程2：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-07-30 22:01:28 线程2：[信息] 账户注册流程已启动: <EMAIL>
2025-07-30 22:01:28 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-07-30 22:01:28 [信息] [线程2] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-07-30 22:01:28 [信息] [线程2] 等待2秒后开始第一次触发...
2025-07-30 22:01:29 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-07-30 22:01:29 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 22:01:29
2025-07-30 22:01:30 [信息] [线程2] 第1次触发邮箱验证码获取...（最多20次）
2025-07-30 22:01:30 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-07-30 22:01:30
2025-07-30 22:01:32 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-07-30 22:01:32 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 22:01:32
2025-07-30 22:01:34 [信息] [线程2] 第2次触发邮箱验证码获取...（最多20次）
2025-07-30 22:01:34 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-07-30 22:01:34
2025-07-30 22:01:35 [信息] [线程1] 第3次触发邮箱验证码获取...（最多20次）
2025-07-30 22:01:35 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 22:01:35
2025-07-30 22:01:37 [信息] [线程2] 第3次触发邮箱验证码获取...（最多20次）
2025-07-30 22:01:37 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-07-30 22:01:37
2025-07-30 22:01:38 [信息] [线程1] 第4次触发邮箱验证码获取...（最多20次）
2025-07-30 22:01:38 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 22:01:38
2025-07-30 22:01:40 [信息] [线程2] 第4次触发邮箱验证码获取...（最多20次）
2025-07-30 22:01:40 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-07-30 22:01:40
2025-07-30 22:01:41 [信息] [线程1] 第5次触发邮箱验证码获取...（最多20次）
2025-07-30 22:01:41 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 22:01:41
2025-07-30 22:01:43 [信息] [线程2] 第5次触发邮箱验证码获取...（最多20次）
2025-07-30 22:01:43 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-07-30 22:01:43
2025-07-30 22:01:44 [信息] [线程1] 第6次触发邮箱验证码获取...（最多20次）
2025-07-30 22:01:44 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 22:01:44
2025-07-30 22:01:46 [信息] [线程2] 第6次触发邮箱验证码获取...（最多20次）
2025-07-30 22:01:46 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-07-30 22:01:46
2025-07-30 22:01:48 [信息] [线程1] 第7次触发邮箱验证码获取...（最多20次）
2025-07-30 22:01:48 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 22:01:48
2025-07-30 22:01:49 [信息] [线程2] 第7次触发邮箱验证码获取...（最多20次）
2025-07-30 22:01:49 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-07-30 22:01:49
2025-07-30 22:01:51 [信息] [线程1] 第8次触发邮箱验证码获取...（最多20次）
2025-07-30 22:01:51 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 22:01:51
2025-07-30 22:01:52 [信息] [线程2] 第8次触发邮箱验证码获取...（最多20次）
2025-07-30 22:01:52 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-07-30 22:01:52
2025-07-30 22:01:54 [信息] [线程1] 第9次触发邮箱验证码获取...（最多20次）
2025-07-30 22:01:54 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 22:01:54
2025-07-30 22:01:55 [信息] [线程2] 第9次触发邮箱验证码获取...（最多20次）
2025-07-30 22:01:55 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-07-30 22:01:55
2025-07-30 22:01:57 [信息] [线程1] 第10次触发邮箱验证码获取...（最多20次）
2025-07-30 22:01:57 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 22:01:57
2025-07-30 22:01:58 [信息] [线程2] 第10次触发邮箱验证码获取...（最多20次）
2025-07-30 22:01:58 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-07-30 22:01:58
2025-07-30 22:01:59 线程1：[信息] [信息] 所有自动线程已停止 (进度: 5%)
2025-07-30 22:01:59 线程1：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 5%)
2025-07-30 22:01:59 线程1：[信息] 已暂停
2025-07-30 22:01:59 [信息] 线程1已暂停
2025-07-30 22:01:59 [信息] 线程1已暂停
2025-07-30 22:02:00 [信息] [线程1] 第11次触发邮箱验证码获取...（最多20次）
2025-07-30 22:02:00 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 22:02:00
2025-07-30 22:02:01 [信息] [线程2] 第11次触发邮箱验证码获取...（最多20次）
2025-07-30 22:02:01 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-07-30 22:02:01
2025-07-30 22:02:03 [信息] [线程1] 第12次触发邮箱验证码获取...（最多20次）
2025-07-30 22:02:03 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 22:02:03
2025-07-30 22:02:04 [信息] [线程2] 第12次触发邮箱验证码获取...（最多20次）
2025-07-30 22:02:04 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-07-30 22:02:04
2025-07-30 22:02:04 [信息] [线程2] 邮箱验证码获取成功: 617175，立即停止重复请求
2025-07-30 22:02:04 [信息] [线程2] 已清理请求文件，停止重复触发
2025-07-30 22:02:04 [信息] [线程2] 已清理响应文件
2025-07-30 22:02:04 线程2：[信息] [信息] 验证码获取成功: 617175，正在自动填入... (进度: 25%)
2025-07-30 22:02:04 线程2：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-07-30 22:02:04 线程2：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-07-30 22:02:04 线程2：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-07-30 22:02:06 [信息] [线程1] 第13次触发邮箱验证码获取...（最多20次）
2025-07-30 22:02:06 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 22:02:06
2025-07-30 22:02:07 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-07-30 22:02:08 线程2：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-07-30 22:02:08 线程2：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-07-30 22:02:08 线程2：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-07-30 22:02:08 线程2：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-07-30 22:02:09 线程2：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-07-30 22:02:10 [信息] [线程1] 第14次触发邮箱验证码获取...（最多20次）
2025-07-30 22:02:10 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 22:02:10
2025-07-30 22:02:12 线程2：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-07-30 22:02:12 线程2：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-07-30 22:02:12 线程2：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-07-30 22:02:13 [信息] [线程1] 第15次触发邮箱验证码获取...（最多20次）
2025-07-30 22:02:13 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 22:02:13
2025-07-30 22:02:15 线程1：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 2 (进度: 5%)
2025-07-30 22:02:15 线程1：[信息] [信息]  进行智能页面检测... (进度: 5%)
2025-07-30 22:02:15 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 5%)
2025-07-30 22:02:15 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 5%)
2025-07-30 22:02:15 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Verify' → 第2页 (进度: 5%)
2025-07-30 22:02:15 线程1：[信息] [信息] 🔍 疑似第2页，进行二次确认... (进度: 5%)
2025-07-30 22:02:15 线程1：[信息] [信息] ✅ 确认为第2页：找到Verification code输入框 (进度: 100%)
2025-07-30 22:02:15 线程1：[信息] [信息]  智能检测到当前在第2页 (进度: 100%)
2025-07-30 22:02:15 线程1：[信息] [信息] 智能检测到当前在第2页，开始智能处理... (进度: 100%)
2025-07-30 22:02:15 线程1：[信息] [信息] 智能检测到第2页，检查邮箱验证码模式... (进度: 100%)
2025-07-30 22:02:15 线程1：[信息] [信息] 邮箱验证码自动模式，继续自动执行... (进度: 100%)
2025-07-30 22:02:15 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-07-30 22:02:15 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-07-30 22:02:15 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-07-30 22:02:15 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-07-30 22:02:15 [信息] [线程1] 已删除旧的请求文件
2025-07-30 22:02:15 线程1：[信息] 已继续
2025-07-30 22:02:15 [信息] [线程1] 已删除旧的响应文件
2025-07-30 22:02:15 [信息] [线程1] 等待2秒后开始第一次触发...
2025-07-30 22:02:15 [信息] 线程1已继续
2025-07-30 22:02:16 [信息] [线程1] 第16次触发邮箱验证码获取...（最多20次）
2025-07-30 22:02:16 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 22:02:16
2025-07-30 22:02:17 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-07-30 22:02:17 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 22:02:17
2025-07-30 22:02:20 [信息] [线程1] 第17次触发邮箱验证码获取...（最多20次）
2025-07-30 22:02:20 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 22:02:20
2025-07-30 22:02:20 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-07-30 22:02:20 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 22:02:20
2025-07-30 22:02:22 线程2：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-07-30 22:02:22 线程2：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-07-30 22:02:23 [信息] [线程1] 第18次触发邮箱验证码获取...（最多20次）
2025-07-30 22:02:23 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 22:02:23
2025-07-30 22:02:23 [信息] [线程1] 第3次触发邮箱验证码获取...（最多20次）
2025-07-30 22:02:23 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 22:02:23
2025-07-30 22:02:26 [信息] [线程1] 第19次触发邮箱验证码获取...（最多20次）
2025-07-30 22:02:26 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 22:02:26
2025-07-30 22:02:26 [信息] [线程1] 邮箱验证码获取成功: 548028，立即停止重复请求
2025-07-30 22:02:26 [信息] [线程1] 已清理请求文件，停止重复触发
2025-07-30 22:02:26 [信息] [线程1] 已清理响应文件
2025-07-30 22:02:26 线程1：[信息] [信息] 验证码获取成功: 548028，正在自动填入... (进度: 100%)
2025-07-30 22:02:26 线程1：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 100%)
2025-07-30 22:02:26 线程1：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 100%)
2025-07-30 22:02:26 [信息] [线程1] 第4次触发邮箱验证码获取...（最多20次）
2025-07-30 22:02:26 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 22:02:26
2025-07-30 22:02:26 线程1：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 100%)
2025-07-30 22:02:29 [信息] [线程1] 第5次触发邮箱验证码获取...（最多20次）
2025-07-30 22:02:29 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 22:02:29
2025-07-30 22:02:29 线程1：[信息] [信息] 等待密码设置页面加载... (进度: 100%)
2025-07-30 22:02:29 线程1：[信息] [信息] 开始填写密码信息... (进度: 100%)
2025-07-30 22:02:29 线程1：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 100%)
2025-07-30 22:02:30 线程1：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 100%)
2025-07-30 22:02:30 线程1：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 100%)
2025-07-30 22:02:31 线程1：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 100%)
2025-07-30 22:02:32 [信息] [线程1] 第6次触发邮箱验证码获取...（最多20次）
2025-07-30 22:02:32 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-30 22:02:32
2025-07-30 22:02:33 [信息] [线程1] 邮箱验证码获取成功: 548028，立即停止重复请求
2025-07-30 22:02:33 [信息] [线程1] 已清理请求文件，停止重复触发
2025-07-30 22:02:33 [信息] [线程1] 已清理响应文件
2025-07-30 22:02:33 线程1：[信息] [信息] 验证码获取成功: 548028，正在自动填入... (进度: 100%)
2025-07-30 22:02:33 线程1：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 100%)
2025-07-30 22:02:34 线程1：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 100%)
2025-07-30 22:02:34 线程1：[信息] [信息] 等待账户类型确认页面加载... (进度: 100%)
2025-07-30 22:02:34 线程1：[信息] [信息] 开始处理账户类型确认... (进度: 100%)
2025-07-30 22:02:46 线程2：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-07-30 22:02:46 线程2：[信息] [信息] 开始后台获取手机号码，同时填写其他信息... (进度: 38%)
2025-07-30 22:02:50 线程2：[信息] [信息] 数据国家代码为BR，需要选择Brazil (进度: 38%)
2025-07-30 22:02:50 线程2：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-07-30 22:02:50 线程1：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 100%)
2025-07-30 22:02:50 线程1：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 100%)
2025-07-30 22:02:50 线程2：[信息] [信息] 后台获取手机号码成功: +524696216740，已保存到注册数据 (进度: 38%)
2025-07-30 22:02:51 线程2：[信息] [信息] 已选择国家: Brazil (进度: 38%)
2025-07-30 22:02:51 线程2：[信息] [信息] 已成功选择国家: Brazil (进度: 38%)
2025-07-30 22:02:51 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-07-30 22:02:52 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-07-30 22:02:54 线程2：[信息] [信息] 已选择国家代码 +52 (进度: 38%)
2025-07-30 22:02:55 线程2：[信息] [信息] 等待后台获取的手机号码结果... (进度: 38%)
2025-07-30 22:02:55 线程2：[信息] [信息] 已自动获取并填入手机号码: +524696216740 (进度: 38%)
2025-07-30 22:02:56 线程2：[信息] [信息] 使用已获取的手机号码: +524696216740（保存本地号码: 4696216740） (进度: 38%)
2025-07-30 22:02:56 线程2：[信息] [信息] 联系信息完成，等待页面加载... (进度: 38%)
2025-07-30 22:02:59 线程2：[信息] [信息] 进入付款信息页面... (进度: 38%)
2025-07-30 22:03:03 线程1：[信息] [信息] 自动获取邮箱验证码出错: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Textbox, new() { Name = "Verification code" }) (进度: 100%)
2025-07-30 22:03:17 线程1：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 100%)
2025-07-30 22:03:17 线程1：[信息] [信息] 开始后台获取手机号码，同时填写其他信息... (进度: 100%)
2025-07-30 22:03:17 线程1：[信息] [信息] 数据国家代码为BR，需要选择Brazil (进度: 100%)
2025-07-30 22:03:18 线程1：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 100%)
2025-07-30 22:03:19 线程1：[信息] [信息] 后台获取手机号码成功: +528999632501，已保存到注册数据 (进度: 100%)
2025-07-30 22:03:19 线程1：[信息] [信息] 已选择国家: Brazil (进度: 100%)
2025-07-30 22:03:19 线程1：[信息] [信息] 已成功选择国家: Brazil (进度: 100%)
2025-07-30 22:03:19 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-07-30 22:03:19 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-07-30 22:03:23 线程1：[信息] [信息] 已选择国家代码 +52 (进度: 100%)
2025-07-30 22:03:23 线程1：[信息] [信息] 等待后台获取的手机号码结果... (进度: 100%)
2025-07-30 22:03:23 线程1：[信息] [信息] 已自动获取并填入手机号码: +528999632501 (进度: 100%)
2025-07-30 22:03:24 线程1：[信息] [信息] 使用已获取的手机号码: +528999632501（保存本地号码: 8999632501） (进度: 100%)
2025-07-30 22:03:25 线程1：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-07-30 22:03:28 线程1：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-07-30 22:03:29 线程2：[信息] [信息] 执行第五页失败: 第五页执行失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Textbox, new() { Name = "Credit or Debit card number" })，但手机号码已保存 (进度: 58%)
2025-07-30 22:03:34 线程1：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-07-30 22:03:34 线程1：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-07-30 22:03:34 线程1：[信息] 已暂停
2025-07-30 22:03:34 [信息] 线程1已暂停
2025-07-30 22:03:34 [信息] 线程1已暂停
2025-07-30 22:03:34 线程2：[信息] [信息] 所有自动线程已停止 (进度: 58%)
2025-07-30 22:03:34 线程2：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 58%)
2025-07-30 22:03:34 线程2：[信息] 已暂停
2025-07-30 22:03:34 [信息] 线程2已暂停
2025-07-30 22:03:34 [信息] 线程2已暂停
2025-07-30 22:03:58 线程1：[信息] [信息] 执行第五页失败: 第五页执行失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Textbox, new() { Name = "Credit or Debit card number" })，但手机号码已保存 (进度: 100%)
2025-07-30 22:15:12 [按钮操作] 终止所有线程 -> 终止所有正在进行的注册流程
2025-07-30 22:15:12 [信息] 开始检查2个线程的终止状态
2025-07-30 22:15:12 [信息] 线程1注册未完成（状态: Paused），将被终止
2025-07-30 22:15:12 [信息] 获取线程1当前数据: <EMAIL>
2025-07-30 22:15:12 [信息] 线程1正在处理数据: <EMAIL>
2025-07-30 22:15:12 [信息] 线程2注册未完成（状态: Paused），将被终止
2025-07-30 22:15:12 [信息] 获取线程2当前数据: <EMAIL>
2025-07-30 22:15:12 [信息] 线程2正在处理数据: <EMAIL>
2025-07-30 22:15:12 [信息] 总共找到2个需要终止的数据: <EMAIL>, <EMAIL>
2025-07-30 22:15:12 [错误] 无法获取主窗口引用，数据移动失败
2025-07-30 22:15:12 线程1：[信息] 终止时正在处理的数据: <EMAIL>
2025-07-30 22:15:12 线程1：[信息] 数据详情: <EMAIL>|U3cC6MIJ|James Shafer|Ambev|Rua Arthur Sander Ferreira 10|Araucria|PR|83704-430|5552700149596328|04|28|075|James Shafer|JI8N1C38Zm3C|BR
2025-07-30 22:15:12 线程1：[信息] 多线程终止 - 复制密钥信息到剪贴板
2025-07-30 22:15:12 线程2：[信息] 终止时正在处理的数据: <EMAIL>
2025-07-30 22:15:12 线程2：[信息] 数据详情: <EMAIL>|vKN51vs1|John Hoffman|WEG|Alameda Grousairus 661|Bauru|SP|17036-300|4040241239740909|10|25|042|John Hoffman|3Z470h5J7Ycv|BR
2025-07-30 22:15:12 线程2：[信息] 多线程终止 - 复制密钥信息到剪贴板
2025-07-30 22:15:12 线程1：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：JI8N1C38Zm3C ③AWS密码：U3cC6MIJ ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-07-30 22:15:12 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：JI8N1C38Zm3C ③AWS密码：U3cC6MIJ ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 22:15:12 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：JI8N1C38Zm3C ③AWS密码：U3cC6MIJ ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 22:15:12 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：JI8N1C38Zm3C ③AWS密码：U3cC6MIJ ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 22:15:13 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：JI8N1C38Zm3C ③AWS密码：U3cC6MIJ ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 22:15:13 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：JI8N1C38Zm3C ③AWS密码：U3cC6MIJ ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 22:15:13 线程1：[信息] [信息] 注册已终止 (进度: 100%)
2025-07-30 22:15:13 线程1：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-07-30 22:15:13 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：3Z470h5J7Ycv ③AWS密码：vKN51vs1 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 98%)
2025-07-30 22:15:13 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：3Z470h5J7Ycv ③AWS密码：vKN51vs1 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 22:15:13 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：3Z470h5J7Ycv ③AWS密码：vKN51vs1 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 22:15:13 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：3Z470h5J7Ycv ③AWS密码：vKN51vs1 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 22:15:13 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：3Z470h5J7Ycv ③AWS密码：vKN51vs1 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 22:15:13 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：3Z470h5J7Ycv ③AWS密码：vKN51vs1 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 22:15:13 [信息] 所有线程已完成，通知主窗口重置状态
2025-07-30 22:15:13 [信息] 多线程状态已重置
2025-07-30 22:15:13 [信息] 所有线程已完成，通知主窗口重置状态
2025-07-30 22:15:13 [信息] 多线程状态已重置
2025-07-30 22:15:13 线程2：[信息] [信息] 注册已终止 (进度: 98%)
2025-07-30 22:15:13 [信息] 所有线程已完成，通知主窗口重置状态
2025-07-30 22:15:13 [信息] 多线程状态已重置
2025-07-30 22:15:14 线程2：[信息] [信息] 所有自动线程已停止 (进度: 98%)
2025-07-30 22:15:14 [信息] 所有线程已完成，通知主窗口重置状态
2025-07-30 22:15:14 [信息] 多线程状态已重置
2025-07-30 22:15:14 线程1：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：JI8N1C38Zm3C ③AWS密码：U3cC6MIJ ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-07-30 22:15:14 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：JI8N1C38Zm3C ③AWS密码：U3cC6MIJ ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 22:15:14 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：JI8N1C38Zm3C ③AWS密码：U3cC6MIJ ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 22:15:14 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：JI8N1C38Zm3C ③AWS密码：U3cC6MIJ ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 22:15:14 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：JI8N1C38Zm3C ③AWS密码：U3cC6MIJ ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 22:15:14 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：JI8N1C38Zm3C ③AWS密码：U3cC6MIJ ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 22:15:14 [信息] 所有线程已完成，通知主窗口重置状态
2025-07-30 22:15:14 [信息] 多线程状态已重置
2025-07-30 22:15:14 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-07-30 22:15:14 线程1：[信息] 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\AWS_Thread_1_20250730_220050
2025-07-30 22:15:14 [信息] 所有线程已完成，通知主窗口重置状态
2025-07-30 22:15:14 [信息] 多线程状态已重置
2025-07-30 22:15:14 线程1：[信息] 已终止
2025-07-30 22:15:14 [信息] 线程1已终止
2025-07-30 22:15:14 [信息] 所有线程已完成，通知主窗口重置状态
2025-07-30 22:15:14 [信息] 多线程状态已重置
2025-07-30 22:15:14 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：3Z470h5J7Ycv ③AWS密码：vKN51vs1 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 98%)
2025-07-30 22:15:14 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：3Z470h5J7Ycv ③AWS密码：vKN51vs1 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 22:15:14 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：3Z470h5J7Ycv ③AWS密码：vKN51vs1 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 22:15:14 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：3Z470h5J7Ycv ③AWS密码：vKN51vs1 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 22:15:14 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：3Z470h5J7Ycv ③AWS密码：vKN51vs1 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 22:15:14 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：3Z470h5J7Ycv ③AWS密码：vKN51vs1 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 22:15:14 [信息] 所有线程已完成，通知主窗口重置状态
2025-07-30 22:15:14 [信息] 多线程状态已重置
2025-07-30 22:15:14 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-07-30 22:15:14 线程2：[信息] 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\AWS_Thread_2_20250730_220050
2025-07-30 22:15:14 [信息] 所有线程已完成，通知主窗口重置状态
2025-07-30 22:15:14 [信息] 多线程状态已重置
2025-07-30 22:15:14 线程2：[信息] 已终止
2025-07-30 22:15:14 [信息] 线程2已终止
2025-07-30 22:15:15 [信息] 多线程窗口引用已清理
2025-07-30 22:15:15 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-07-30 22:15:15 [信息] 多线程管理窗口正在关闭
2025-07-30 22:42:21 [信息] AWS自动注册工具启动
2025-07-30 22:42:21 [信息] 程序版本: 1.0.0.0
2025-07-30 22:42:21 [信息] 启动时间: 2025-07-30 22:42:21
2025-07-30 22:42:21 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-07-30 22:42:21 [信息] 线程数量已选择: 1
2025-07-30 22:42:21 [信息] 线程数量选择初始化完成
2025-07-30 22:42:21 [信息] 程序初始化完成
2025-07-30 22:42:24 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-07-30 22:42:26 [信息] 已选择文件: C:\Users\<USER>\Desktop\BR.txt
2025-07-30 22:42:28 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-07-30 22:42:30 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-07-30-巴西.txt
2025-07-30 22:42:30 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-07-30-巴西.txt
2025-07-30 22:42:31 [信息] 成功加载 1 条数据
2025-07-30 22:42:35 [按钮操作] 开始注册 -> 启动注册流程
2025-07-30 22:42:35 [系统状态] 正在检测Chrome浏览器(无痕模式)...
2025-07-30 22:42:36 [信息] 单线程模式窗口布局: 使用固定位置(0, 0), 大小(600x400)
2025-07-30 22:42:36 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36
2025-07-30 22:42:36 [系统状态] 尝试启动系统Chrome(无痕模式)...
2025-07-30 22:42:37 [系统状态] 获取无痕模式页面...
2025-07-30 22:42:40 [系统状态] 创建新的无痕模式页面
2025-07-30 22:42:40 [系统状态] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用

2025-07-30 22:42:40 [注册开始] 邮箱: <EMAIL>, 索引: 1/1
2025-07-30 22:42:40 [系统状态] 已清空AWS密钥变量和MFA变量
2025-07-30 22:42:40 [系统状态] 开始新的注册: <EMAIL>，已清空之前的号码状态
2025-07-30 22:42:40 [系统状态] 在现有窗口中新建标签页...
2025-07-30 22:42:40 [系统状态] 使用现有的浏览器上下文
2025-07-30 22:42:40 [系统状态] 正在新建标签页...
2025-07-30 22:42:40 [系统状态] 已设置页面视口大小为600x400
2025-07-30 22:42:40 [系统状态] 验证无痕Chrome模式状态...
2025-07-30 22:42:40 [系统状态] 无痕Chrome检测: ✓ 已启用
2025-07-30 22:42:40 [系统状态] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb...
2025-07-30 22:42:40 [系统状态] 正在打开AWS注册页面...
2025-07-30 22:43:00 [系统状态] 正在执行第一页注册...
2025-07-30 22:43:00 [系统状态] 🔍 等待第一页加载完成...
2025-07-30 22:43:00 [系统状态] ✅ 第一页加载完成，找到验证邮箱按钮
2025-07-30 22:43:00 [信息] 第一页加载完成，找到验证邮箱按钮
2025-07-30 22:43:00 [系统状态] 📋 第一页基本信息填写完成，检查页面响应...
2025-07-30 22:43:03 [系统状态] 🔍 检查是否出现IP异常错误...
2025-07-30 22:43:03 [系统状态] ✅ 未检测到IP异常错误，继续流程
2025-07-30 22:43:03 [系统状态] 🔍 开始检查第一页是否有图形验证码（2次检测）...
2025-07-30 22:43:03 [系统状态] 🔍 第1次检测图形验证码...
2025-07-30 22:43:04 [系统状态] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个
2025-07-30 22:43:04 [系统状态] ⏳ 第1次未发现验证码，等待2秒后继续检测...
2025-07-30 22:43:06 [系统状态] 🔍 第2次检测图形验证码...
2025-07-30 22:43:06 [系统状态] 🔍 第2次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个
2025-07-30 22:43:06 [系统状态] ✅ 第2次检测发现图形验证码！
2025-07-30 22:43:06 [系统状态] 🔍 第一页图形验证码最终检测结果: 发现验证码
2025-07-30 22:43:06 [系统状态] ⚠️ 第一页检测到图形验证码，开始处理...
2025-07-30 22:43:06 [系统状态] 第一页图形验证码自动识别模式
2025-07-30 22:43:06 [系统状态] 第一页第1次尝试自动识别图形验证码...
2025-07-30 22:43:13 [系统状态] 第一页已从iframe截取验证码图片，大小: 35222 字节
2025-07-30 22:43:13 [系统状态] ✅ 图片验证通过：201x71px，35222字节，复杂度符合要求
2025-07-30 22:43:13 [系统状态] 正在调用云打码API识别验证码...
2025-07-30 22:43:25 [系统状态] 🔍 API响应内容: {"msg":"识别成功","code":10000,"data":{"code":0,"data":"zhnnw36","time":10.314061641693115,"externel":2,"file_path":"https:\/\/ali-jfb2024.oss-cn-chengdu.aliyuncs.com\/jfb_upload\/dabiao\/2025\/07\/b3eb98cfee3312ad50be4890e87c6fbb.png","order_unique_id":"b3eb98cfee3312ad50be4890e87c6fbb","reduce_score":18,"unique_code":"b3eb98cfee3312ad50be4890e87c6fbb"}}
2025-07-30 22:43:25 [系统状态] 第一页第1次识别结果: zhnnw36 → 转换为小写: zhnnw36
2025-07-30 22:43:25 [系统状态] 第一页使用iframe内GetByLabel选择器
2025-07-30 22:43:25 [系统状态] 已填入验证码: zhnnw36
2025-07-30 22:43:25 [系统状态] 已点击iframe内Submit按钮
2025-07-30 22:43:28 [系统状态] 第一页第1次图形验证码识别结果错误，等待新验证码
2025-07-30 22:43:28 [系统状态] 第一页第2次失败，等待新验证码...
2025-07-30 22:43:30 [系统状态] 第一页第2次尝试自动识别图形验证码...
2025-07-30 22:43:33 [系统状态] 第一页已从iframe截取验证码图片，大小: 35554 字节
2025-07-30 22:43:33 [系统状态] ✅ 图片验证通过：201x71px，35554字节，复杂度符合要求
2025-07-30 22:43:33 [系统状态] 正在调用云打码API识别验证码...
2025-07-30 22:43:46 [系统状态] 🔍 API响应内容: {"msg":"识别成功","code":10000,"data":{"code":0,"data":"7msf74","time":13.259634733200073,"externel":2,"file_path":"https:\/\/ali-jfb2024.oss-cn-chengdu.aliyuncs.com\/jfb_upload\/dabiao\/2025\/07\/a863211f282aa8dc23b8fe56772bad77.png","order_unique_id":"a863211f282aa8dc23b8fe56772bad77","reduce_score":16,"unique_code":"a863211f282aa8dc23b8fe56772bad77"}}
2025-07-30 22:43:46 [系统状态] 第一页第2次识别结果: 7msf74 → 转换为小写: 7msf74
2025-07-30 22:43:46 [系统状态] 第一页使用iframe内GetByLabel选择器
2025-07-30 22:43:46 [系统状态] 已填入验证码: 7msf74
2025-07-30 22:43:47 [系统状态] 已点击iframe内Submit按钮
2025-07-30 22:43:49 [系统状态] 第一页第2次图形验证码识别成功
2025-07-30 22:43:49 [系统状态] 第一页图形验证码自动完成
2025-07-30 22:43:49 [系统状态] 📋 第一页图形验证码检查完成
2025-07-30 22:43:49 [系统状态] 第一页完成，等待验证码页面...
2025-07-30 22:43:49 [系统状态] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True
2025-07-30 22:43:49 [系统状态] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码...
2025-07-30 22:43:49 [系统状态] 正在后台自动获取验证码，继续注册按钮已禁用...
2025-07-30 22:44:31 [按钮操作] 暂停注册 -> 暂停当前注册流程
2025-07-30 22:44:31 [系统状态] 所有自动线程已停止
2025-07-30 22:44:31 [系统状态] 注册已暂停，所有自动线程已停止
2025-07-30 22:44:32 [按钮操作] 继续注册 -> 继续执行注册流程
2025-07-30 22:44:32 [系统状态]  继续注册被调用，当前状态: Paused，当前步骤: 2
2025-07-30 22:44:32 [系统状态]  进行智能页面检测...
2025-07-30 22:44:32 [系统状态] 🔍 开始智能页面检测（按钮优先策略）...
2025-07-30 22:44:32 [系统状态] 📋 获取页面所有按钮和链接元素...
2025-07-30 22:44:32 [系统状态] ⚠️ 未找到匹配的页面按钮或链接
2025-07-30 22:44:32 [系统状态] ⚠️ 智能检测失败，启用详细页面分析...
2025-07-30 22:44:32 [系统状态] 🔬 执行详细页面分析...
2025-07-30 22:44:32 [系统状态] 📄 页面URL: https://signin.aws.amazon.com/signup?request_type=register
2025-07-30 22:44:32 [系统状态] 📋 页面标题: AWS Console - Signup
2025-07-30 22:44:32 [系统状态] 📊 分析结果: 第三页-密码设置(2/3个元素匹配)
2025-07-30 22:44:32 [系统状态]  智能检测到当前在第3页
2025-07-30 22:44:32 [系统状态] 智能检测到当前在第3页，开始智能处理...
2025-07-30 22:44:37 [系统状态] 等待密码设置页面加载...
2025-07-30 22:44:37 [系统状态] 开始填写密码信息...
2025-07-30 22:44:37 [系统状态] 第一个密码输入框已清空并重新填写完成
2025-07-30 22:44:37 [系统状态] 确认密码输入框已清空并重新填写完成
2025-07-30 22:44:37 [系统状态] 密码填写完成，点击继续按钮...
2025-07-30 22:44:39 [系统状态] 密码设置完成，等待页面跳转...
2025-07-30 22:44:42 [系统状态] 第三页完成，进入第3.5页（账户类型确认页面）...
2025-07-30 22:44:42 [系统状态] 等待账户类型确认页面加载...
2025-07-30 22:44:42 [系统状态] 开始处理账户类型确认...
2025-07-30 22:44:56 [系统状态] 已点击Choose paid plan按钮，账户类型确认完成
2025-07-30 22:44:56 [系统状态] 账户类型确认完成，进入联系信息页面...
2025-07-30 22:45:23 [系统状态] 第3.5页完成，页面已跳转到第4页
2025-07-30 22:45:23 [系统状态] 开始后台获取手机号码，同时填写其他信息...
2025-07-30 22:45:23 [系统状态] 数据国家代码为BR，需要选择Brazil
2025-07-30 22:45:24 [系统状态] 已点击国家/地区选择器，正在展开列表...
2025-07-30 22:45:25 [系统状态] 后台获取手机号码成功: +526681693674，已保存到注册数据
2025-07-30 22:45:26 [系统状态] 已选择国家: Brazil
2025-07-30 22:45:26 [系统状态] 已成功选择国家: Brazil
2025-07-30 22:45:26 [系统状态] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)...
2025-07-30 22:45:26 [系统状态] 已点击国家代码按钮，正在展开列表...
2025-07-30 22:45:29 [系统状态] 已选择国家代码 +52
2025-07-30 22:45:30 [系统状态] 等待后台获取的手机号码结果...
2025-07-30 22:45:30 [系统状态] 已自动获取并填入手机号码: +526681693674
2025-07-30 22:45:31 [系统状态] 使用已获取的手机号码: +526681693674（保存本地号码: 6681693674）
2025-07-30 22:45:31 [系统状态] 联系信息完成，等待页面加载...
2025-07-30 22:45:35 [系统状态] 进入付款信息页面...
2025-07-30 22:45:51 [系统状态] 邮箱验证码自动获取失败: 获取验证码超时（2分钟内未获取到有效验证码）
2025-07-30 22:45:51 [系统状态] 🔴 Microsoft获取验证码失败，转为手动模式
2025-07-30 22:46:05 [系统状态] 执行第五页失败: 第五页执行失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Textbox, new() { Name = "Credit or Debit card number" })，但手机号码已保存
2025-07-30 22:49:26 [按钮操作] 暂停注册 -> 暂停当前注册流程
2025-07-30 22:49:26 [系统状态] 所有自动线程已停止
2025-07-30 22:49:26 [系统状态] 注册已暂停，所有自动线程已停止
2025-07-30 22:53:15 [按钮操作] 终止注册 -> 终止当前注册流程
2025-07-30 22:53:15 [系统状态] 所有自动线程已停止
2025-07-30 22:53:15 [信息] 检测到所有数据处理完成，已完全重置按钮状态
2025-07-30 22:53:15 [系统状态] 注册已终止
2025-07-30 22:53:15 [注册结束] 注册被终止 - 邮箱: <EMAIL>
2025-07-30 22:53:15 [系统状态] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：b305C0n45f ③AWS密码：6j3nJ8Ft ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 22:53:15 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：b305C0n45f ③AWS密码：6j3nJ8Ft ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 22:53:15 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：b305C0n45f ③AWS密码：6j3nJ8Ft ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-30 22:53:15 [系统状态] 已清空AWS密钥变量和MFA变量
