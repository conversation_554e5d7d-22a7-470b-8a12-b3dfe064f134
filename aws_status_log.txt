[2025-07-30 23:15:29] [INFO] 设置Qt插件路径: D:\Python311\Lib\site-packages\PyQt5\Qt5\plugins
[2025-07-30 23:15:29] [INFO] === AWS工具启动 ===
[2025-07-30 23:15:29] [INFO] Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
[2025-07-30 23:15:29] [INFO] 系统平台: win32
[2025-07-30 23:15:29] [INFO] 已设置Windows任务栏图标 AppID: aws.verification.tool.1.0
[2025-07-30 23:15:30] [INFO] AWS文件监控已启动
[2025-07-30 23:15:30] [INFO] 已设置Windows任务栏图标 AppID: aws.verification.tool.1.0
[2025-07-30 23:15:30] [INFO] === AWS工具启动 ===
[2025-07-30 23:15:30] [INFO] Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
[2025-07-30 23:15:30] [INFO] 系统平台: win32
[2025-07-30 23:15:31] [INFO] 软件已初始化，请按照上方指南操作
[2025-07-30 23:15:36] [INFO] 更新剩余账户数量: 10
[2025-07-30 23:15:36] [INFO] 已选择文件: C:/Users/<USER>/Desktop/BR.txt
[2025-07-30 23:15:38] [INFO] 解析结果: 未使用账户 10个, 成功账户 0个, 失败账户 1个
[2025-07-30 23:15:38] [INFO] 从文件中解析出 10 个账户
[2025-07-30 23:15:38] [DEBUG] 已排除 0 个成功登录的账户
[2025-07-30 23:15:38] [DEBUG] 已排除 1 个登录失败的账户
[2025-07-30 23:15:38] [DEBUG] 筛选后可用账户数量: 10
[2025-07-30 23:15:38] [INFO] 开始处理 1 个账户
[2025-07-30 23:15:38] [INFO] 账户 <EMAIL> 状态: 登录中
[2025-07-30 23:15:44] [SUCCESS] 账户 <EMAIL> 登录成功
[2025-07-30 23:15:44] [SUCCESS] 账户 <EMAIL> 已标记为登录成功
[2025-07-30 23:15:44] [INFO] 账户 <EMAIL> 状态: 登录成功
[2025-07-30 23:15:44] [SUCCESS] 处理完成，成功登录 1 个账户
[2025-07-30 23:15:44] [ERROR] 处理完成，登录失败 1 个账户
[2025-07-30 23:15:44] [DEBUG] 调试：成功账户邮箱列表 ['<EMAIL>']
[2025-07-30 23:15:44] [DEBUG] 调试：失败账户邮箱列表 ['<EMAIL>']
[2025-07-30 23:15:44] [INFO] 文件更新完成：成功账户 1，失败账户 1，未处理账户 9
[2025-07-30 23:15:44] [DEBUG] 调试信息：成功账户列表长度 1
[2025-07-30 23:15:44] [DEBUG] 调试信息：第一个成功账户 ['<EMAIL>', '8ixPco8L6', 'M.C537_BAY.0.U.-CumPqWRFp3x*AjgYKiFuDDBdpdMDn2rWLBWLqGS5jou57IASdscF37N6OjnYfSvtIb0kAxebmVVu8BRJtMtz6BsqHrN0XlgSArvfx1GhCKtz31lkDGhlHePanK*KIMw4*1Yy*IDaJozTqtcsrhvRk2wpEYo518hrv2whel4ggRiLHsJJutLbf5Qqf5XHianlXMpCaFR7Nsjw*onvpFOVtrLn9IMk212DvpEit!qEZHnNBmKDKFOutnWD1dvNaKanXbnP7ckMRWYkyLlyl3ZpiQBBvZPOfzunY0gkdPljhG1VyE8XopFTyOwGNUjz50b!fjferjpKA7C2Sfc0LAvRpDNhsJWrOpwEE7QEfe5RGPpPleMiJtan2F8k6Rd3r7I2eMgLa5V80VAEFs8bIa!bgNN4ybHQewU0dJxCL3PCMUOk', '9e5f94bc-e8a4-4e73-b8be-63364c29d753']
[2025-07-30 23:15:44] [INFO] 已更新文件内容
[2025-07-30 23:15:44] [INFO] 更新剩余账户数量: 9
[2025-07-30 23:15:54] [INFO] 解析结果: 未使用账户 11个, 成功账户 0个, 失败账户 0个
[2025-07-30 23:15:54] [INFO] 从文件中解析出 11 个账户
[2025-07-30 23:15:54] [DEBUG] 已排除 0 个成功登录的账户
[2025-07-30 23:15:54] [DEBUG] 已排除 0 个登录失败的账户
[2025-07-30 23:15:54] [DEBUG] 筛选后可用账户数量: 11
[2025-07-30 23:15:54] [INFO] 开始处理 1 个账户
[2025-07-30 23:15:54] [INFO] 账户 <EMAIL> 状态: 登录中
[2025-07-30 23:16:02] [SUCCESS] 账户 <EMAIL> 登录成功
[2025-07-30 23:16:02] [SUCCESS] 账户 <EMAIL> 已标记为登录成功
[2025-07-30 23:16:02] [INFO] 账户 <EMAIL> 状态: 登录成功
[2025-07-30 23:16:02] [SUCCESS] 处理完成，成功登录 1 个账户
[2025-07-30 23:16:02] [DEBUG] 调试：成功账户邮箱列表 ['<EMAIL>']
[2025-07-30 23:16:02] [DEBUG] 调试：失败账户邮箱列表 []
[2025-07-30 23:16:02] [INFO] 文件更新完成：成功账户 1，失败账户 0，未处理账户 10
[2025-07-30 23:16:02] [DEBUG] 调试信息：成功账户列表长度 1
[2025-07-30 23:16:02] [DEBUG] 调试信息：第一个成功账户 ['<EMAIL>', '10r6w36Y58U', 'M.C546_BAY.0.U.-CtJzTc6Odih206qGXabfe0ZJo3XLc3RXsyXFq8lz1LhmZLgNTJ35xbjmCPSe5JZgdgbUQj3s4M5wB7fsQu7ZgLIBYfi94DppCXImaHLJTBIa3u4stt5oNlutly9usYwx6snG8Dj8KNKwpOJEnEknNF6blfX5ImY3SpFTtjKeF1DzsrX2HRjLhZruWdmmjD6!MMSe!7Wk2L03FK4Xggm*BZ95iob3BaFi0HpD7iF6piPbhTl6sk*sNihVEMRZyD9*dsDqyh4f2xyKMAPg!lzD92D1FNB9m17bhI37jHguci6YB0gQPs*2akNFBObPGUOSMGCwqrmbeeZGDHwxpcd4GSwJf18AIWc7Fm9nLsmE3Zg71GULOQNYdu!DAyBwU3szwbaTWQgVj5BaPll475wXQHwir0NRcRO0uKMjnL1yaijd', '9e5f94bc-e8a4-4e73-b8be-63364c29d753']
[2025-07-30 23:16:02] [INFO] 已更新文件内容
[2025-07-30 23:16:02] [INFO] 更新剩余账户数量: 10
[2025-07-30 23:16:06] [INFO] 尝试为 <EMAIL> 查询状态
[2025-07-30 23:16:06] [DEBUG] 初始化账户状态查询器: <EMAIL>
[2025-07-30 23:16:06] [INFO] 成功启动状态查询线程: <EMAIL>
[2025-07-30 23:16:12] [DEBUG] 更新状态查询结果: 行0, 状态状态查询成功, 结果无100$账号
[2025-07-30 23:16:12] [INFO] 账户无100$状态: 行1
[2025-07-30 23:16:12] [DEBUG] 已恢复行0的按钮状态
[2025-07-30 23:16:12] [INFO] 行 1: 状态查询成功, 结果: 无100$账号
[2025-07-30 23:16:12] [DEBUG] 线程仍在运行，稍后清理: 行0
[2025-07-30 23:16:17] [ERROR] 延迟清理线程引用时出错: wrapped C/C++ object of type QThread has been deleted
[2025-07-30 23:16:45] [INFO] 解析结果: 未使用账户 12个, 成功账户 0个, 失败账户 0个
[2025-07-30 23:16:45] [INFO] 从文件中解析出 12 个账户
[2025-07-30 23:16:45] [DEBUG] 已排除 0 个成功登录的账户
[2025-07-30 23:16:45] [DEBUG] 已排除 0 个登录失败的账户
[2025-07-30 23:16:45] [DEBUG] 筛选后可用账户数量: 12
[2025-07-30 23:16:45] [INFO] 开始处理 1 个账户
[2025-07-30 23:16:45] [INFO] 账户 <EMAIL> 状态: 登录中
[2025-07-30 23:16:49] [SUCCESS] 账户 <EMAIL> 登录成功
[2025-07-30 23:16:49] [SUCCESS] 账户 <EMAIL> 已标记为登录成功
[2025-07-30 23:16:49] [INFO] 账户 <EMAIL> 状态: 登录成功
[2025-07-30 23:16:49] [SUCCESS] 处理完成，成功登录 1 个账户
[2025-07-30 23:16:49] [DEBUG] 调试：成功账户邮箱列表 ['<EMAIL>']
[2025-07-30 23:16:49] [DEBUG] 调试：失败账户邮箱列表 []
[2025-07-30 23:16:49] [INFO] 文件更新完成：成功账户 1，失败账户 0，未处理账户 11
[2025-07-30 23:16:49] [DEBUG] 调试信息：成功账户列表长度 1
[2025-07-30 23:16:49] [DEBUG] 调试信息：第一个成功账户 ['<EMAIL>', 'E4aT737wmII', 'M.C541_BL2.0.U.-CsZjU7cwC7VM9*A5A13pCt13BZXU2GrmIorcdDigS8TrPfkOAeh7W4b7LBG3MNnVBZWMOfdgvtzGHgBt3!LWxWtSZ5AlL3sCgQCLtdJUkuLvk68DTnejKdy2qK*VI1hsiAi0Hwl19EKlYCYu8z4VM4hh7xr7A2fO4K*xkc!KWyYYH2ewvZZUbYZt7NHaatwQPZyguljHFUqyC9Wl50jmVhFKWxdgplBu71uipjKnXkswU2uBYpkKsCbYfQEcgOIlm2rXTevDDs3cKqVbjeuYMU5i*bVGvrymVZr1Duw8eetVFB1YVEbUs2ODSOsYLdta0RQPBfr9Hl6k2rqhw!XQxXkZ4iQcTJaifTaAEmI1Bc!43kZWCJqq3CHQokZh6deCBdAjzTBnVSy9uLyKCgp0OggQKb0H72P54uJns8C8zWql', '9e5f94bc-e8a4-4e73-b8be-63364c29d753']
[2025-07-30 23:16:49] [INFO] 已更新文件内容
[2025-07-30 23:16:49] [INFO] 更新剩余账户数量: 11
[2025-07-30 23:16:52] [INFO] 尝试为 <EMAIL> 查询状态
[2025-07-30 23:16:52] [DEBUG] 初始化账户状态查询器: <EMAIL>
[2025-07-30 23:16:52] [ERROR] 停止旧线程时出错: wrapped C/C++ object of type QThread has been deleted
[2025-07-30 23:16:52] [INFO] 成功启动状态查询线程: <EMAIL>
[2025-07-30 23:16:58] [DEBUG] 更新状态查询结果: 行0, 状态状态查询成功, 结果无100$账号
[2025-07-30 23:16:58] [INFO] 账户无100$状态: 行1
[2025-07-30 23:16:58] [DEBUG] 已恢复行0的按钮状态
[2025-07-30 23:16:58] [INFO] 行 1: 状态查询成功, 结果: 无100$账号
[2025-07-30 23:16:58] [DEBUG] 线程仍在运行，稍后清理: 行0
[2025-07-30 23:17:03] [ERROR] 延迟清理线程引用时出错: wrapped C/C++ object of type QThread has been deleted
[2025-07-30 23:17:57] [INFO] 停止行 0 的验证码获取线程
[2025-07-30 23:17:57] [INFO] 已停止文件监控
[2025-07-30 23:17:57] [INFO] === AWS工具关闭 ===
[2025-07-30 23:18:00] [INFO] 设置Qt插件路径: D:\Python311\Lib\site-packages\PyQt5\Qt5\plugins
[2025-07-30 23:18:00] [INFO] === AWS工具启动 ===
[2025-07-30 23:18:00] [INFO] Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
[2025-07-30 23:18:00] [INFO] 系统平台: win32
[2025-07-30 23:18:00] [INFO] 已设置Windows任务栏图标 AppID: aws.verification.tool.1.0
[2025-07-30 23:18:00] [INFO] AWS文件监控已启动
[2025-07-30 23:18:00] [INFO] 已设置Windows任务栏图标 AppID: aws.verification.tool.1.0
[2025-07-30 23:18:00] [INFO] === AWS工具启动 ===
[2025-07-30 23:18:00] [INFO] Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
[2025-07-30 23:18:00] [INFO] 系统平台: win32
[2025-07-30 23:18:01] [INFO] 软件已初始化，请按照上方指南操作
[2025-07-30 23:18:09] [INFO] 更新剩余账户数量: 12
[2025-07-30 23:18:09] [INFO] 已选择文件: C:/Users/<USER>/Desktop/BR.txt
[2025-07-30 23:18:10] [INFO] 解析结果: 未使用账户 12个, 成功账户 0个, 失败账户 0个
[2025-07-30 23:18:10] [INFO] 从文件中解析出 12 个账户
[2025-07-30 23:18:11] [DEBUG] 已排除 0 个成功登录的账户
[2025-07-30 23:18:11] [DEBUG] 已排除 0 个登录失败的账户
[2025-07-30 23:18:11] [DEBUG] 筛选后可用账户数量: 12
[2025-07-30 23:18:11] [INFO] 开始处理 1 个账户
[2025-07-30 23:18:11] [INFO] 账户 <EMAIL> 状态: 登录中
[2025-07-30 23:18:15] [SUCCESS] 账户 <EMAIL> 登录成功
[2025-07-30 23:18:15] [SUCCESS] 账户 <EMAIL> 已标记为登录成功
[2025-07-30 23:18:15] [INFO] 账户 <EMAIL> 状态: 登录成功
[2025-07-30 23:18:15] [SUCCESS] 处理完成，成功登录 1 个账户
[2025-07-30 23:18:15] [DEBUG] 调试：成功账户邮箱列表 ['<EMAIL>']
[2025-07-30 23:18:15] [DEBUG] 调试：失败账户邮箱列表 []
[2025-07-30 23:18:15] [INFO] 文件更新完成：成功账户 1，失败账户 0，未处理账户 11
[2025-07-30 23:18:15] [DEBUG] 调试信息：成功账户列表长度 1
[2025-07-30 23:18:15] [DEBUG] 调试信息：第一个成功账户 ['<EMAIL>', 'E4aT737wmII', 'M.C541_BL2.0.U.-CsZjU7cwC7VM9*A5A13pCt13BZXU2GrmIorcdDigS8TrPfkOAeh7W4b7LBG3MNnVBZWMOfdgvtzGHgBt3!LWxWtSZ5AlL3sCgQCLtdJUkuLvk68DTnejKdy2qK*VI1hsiAi0Hwl19EKlYCYu8z4VM4hh7xr7A2fO4K*xkc!KWyYYH2ewvZZUbYZt7NHaatwQPZyguljHFUqyC9Wl50jmVhFKWxdgplBu71uipjKnXkswU2uBYpkKsCbYfQEcgOIlm2rXTevDDs3cKqVbjeuYMU5i*bVGvrymVZr1Duw8eetVFB1YVEbUs2ODSOsYLdta0RQPBfr9Hl6k2rqhw!XQxXkZ4iQcTJaifTaAEmI1Bc!43kZWCJqq3CHQokZh6deCBdAjzTBnVSy9uLyKCgp0OggQKb0H72P54uJns8C8zWql', '9e5f94bc-e8a4-4e73-b8be-63364c29d753']
[2025-07-30 23:18:15] [INFO] 已更新文件内容
[2025-07-30 23:18:15] [INFO] 更新剩余账户数量: 11
[2025-07-30 23:18:17] [INFO] 尝试为 <EMAIL> 查询状态
[2025-07-30 23:18:17] [DEBUG] 初始化账户状态查询器: <EMAIL>
[2025-07-30 23:18:17] [INFO] 成功启动状态查询线程: <EMAIL>
[2025-07-30 23:18:27] [DEBUG] 更新状态查询结果: 行0, 状态状态查询成功, 结果无100$账号
[2025-07-30 23:18:27] [INFO] 账户无100$状态: 行1
[2025-07-30 23:18:27] [DEBUG] 已恢复行0的按钮状态
[2025-07-30 23:18:27] [INFO] 行 1: 状态查询成功, 结果: 无100$账号
[2025-07-30 23:18:27] [DEBUG] 线程仍在运行，稍后清理: 行0
[2025-07-30 23:18:32] [ERROR] 延迟清理线程引用时出错: wrapped C/C++ object of type QThread has been deleted
[2025-07-30 23:19:36] [INFO] 停止行 0 的验证码获取线程
[2025-07-30 23:19:36] [INFO] 已停止文件监控
[2025-07-30 23:19:36] [INFO] === AWS工具关闭 ===
[2025-07-30 23:19:38] [INFO] 设置Qt插件路径: D:\Python311\Lib\site-packages\PyQt5\Qt5\plugins
[2025-07-30 23:19:38] [INFO] === AWS工具启动 ===
[2025-07-30 23:19:38] [INFO] Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
[2025-07-30 23:19:38] [INFO] 系统平台: win32
[2025-07-30 23:19:38] [INFO] 已设置Windows任务栏图标 AppID: aws.verification.tool.1.0
[2025-07-30 23:19:39] [INFO] AWS文件监控已启动
[2025-07-30 23:19:39] [INFO] 已设置Windows任务栏图标 AppID: aws.verification.tool.1.0
[2025-07-30 23:19:39] [INFO] === AWS工具启动 ===
[2025-07-30 23:19:39] [INFO] Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
[2025-07-30 23:19:39] [INFO] 系统平台: win32
[2025-07-30 23:19:39] [INFO] 软件已初始化，请按照上方指南操作
[2025-07-30 23:19:46] [INFO] 更新剩余账户数量: 12
[2025-07-30 23:19:46] [INFO] 已选择文件: C:/Users/<USER>/Desktop/BR.txt
[2025-07-30 23:19:47] [INFO] 解析结果: 未使用账户 12个, 成功账户 0个, 失败账户 0个
[2025-07-30 23:19:47] [INFO] 从文件中解析出 12 个账户
[2025-07-30 23:19:47] [DEBUG] 已排除 0 个成功登录的账户
[2025-07-30 23:19:47] [DEBUG] 已排除 0 个登录失败的账户
[2025-07-30 23:19:47] [DEBUG] 筛选后可用账户数量: 12
[2025-07-30 23:19:47] [INFO] 开始处理 1 个账户
[2025-07-30 23:19:47] [INFO] 账户 <EMAIL> 状态: 登录中
[2025-07-30 23:19:52] [SUCCESS] 账户 <EMAIL> 登录成功
[2025-07-30 23:19:52] [SUCCESS] 账户 <EMAIL> 已标记为登录成功
[2025-07-30 23:19:52] [INFO] 账户 <EMAIL> 状态: 登录成功
[2025-07-30 23:19:52] [SUCCESS] 处理完成，成功登录 1 个账户
[2025-07-30 23:19:52] [DEBUG] 调试：成功账户邮箱列表 ['<EMAIL>']
[2025-07-30 23:19:52] [DEBUG] 调试：失败账户邮箱列表 []
[2025-07-30 23:19:52] [INFO] 文件更新完成：成功账户 1，失败账户 0，未处理账户 11
[2025-07-30 23:19:52] [DEBUG] 调试信息：成功账户列表长度 1
[2025-07-30 23:19:52] [DEBUG] 调试信息：第一个成功账户 ['<EMAIL>', 'E4aT737wmII', 'M.C541_BL2.0.U.-CsZjU7cwC7VM9*A5A13pCt13BZXU2GrmIorcdDigS8TrPfkOAeh7W4b7LBG3MNnVBZWMOfdgvtzGHgBt3!LWxWtSZ5AlL3sCgQCLtdJUkuLvk68DTnejKdy2qK*VI1hsiAi0Hwl19EKlYCYu8z4VM4hh7xr7A2fO4K*xkc!KWyYYH2ewvZZUbYZt7NHaatwQPZyguljHFUqyC9Wl50jmVhFKWxdgplBu71uipjKnXkswU2uBYpkKsCbYfQEcgOIlm2rXTevDDs3cKqVbjeuYMU5i*bVGvrymVZr1Duw8eetVFB1YVEbUs2ODSOsYLdta0RQPBfr9Hl6k2rqhw!XQxXkZ4iQcTJaifTaAEmI1Bc!43kZWCJqq3CHQokZh6deCBdAjzTBnVSy9uLyKCgp0OggQKb0H72P54uJns8C8zWql', '9e5f94bc-e8a4-4e73-b8be-63364c29d753']
[2025-07-30 23:19:52] [INFO] 已更新文件内容
[2025-07-30 23:19:52] [INFO] 更新剩余账户数量: 11
[2025-07-30 23:19:54] [INFO] 尝试为 <EMAIL> 查询状态
[2025-07-30 23:19:54] [DEBUG] 初始化账户状态查询器: <EMAIL>
[2025-07-30 23:19:54] [INFO] 成功启动状态查询线程: <EMAIL>
[2025-07-30 23:19:59] [DEBUG] 更新状态查询结果: 行0, 状态状态查询成功, 结果无100$账号
[2025-07-30 23:19:59] [INFO] 账户无100$状态: 行1
[2025-07-30 23:19:59] [DEBUG] 已恢复行0的按钮状态
[2025-07-30 23:19:59] [INFO] 行 1: 状态查询成功, 结果: 无100$账号
[2025-07-30 23:19:59] [DEBUG] 线程仍在运行，稍后清理: 行0
[2025-07-30 23:20:01] [INFO] 停止行 0 的验证码获取线程
[2025-07-30 23:20:01] [INFO] 已停止文件监控
[2025-07-30 23:20:01] [INFO] === AWS工具关闭 ===
[2025-07-30 23:20:36] [INFO] 设置Qt插件路径: D:\Python311\Lib\site-packages\PyQt5\Qt5\plugins
[2025-07-30 23:20:36] [INFO] === AWS工具启动 ===
[2025-07-30 23:20:36] [INFO] Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
[2025-07-30 23:20:36] [INFO] 系统平台: win32
[2025-07-30 23:20:36] [INFO] 已设置Windows任务栏图标 AppID: aws.verification.tool.1.0
[2025-07-30 23:20:37] [INFO] AWS文件监控已启动
[2025-07-30 23:20:37] [INFO] 已设置Windows任务栏图标 AppID: aws.verification.tool.1.0
[2025-07-30 23:20:37] [INFO] === AWS工具启动 ===
[2025-07-30 23:20:37] [INFO] Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
[2025-07-30 23:20:37] [INFO] 系统平台: win32
[2025-07-30 23:20:37] [INFO] 软件已初始化，请按照上方指南操作
[2025-07-30 23:20:43] [INFO] 更新剩余账户数量: 12
[2025-07-30 23:20:43] [INFO] 已选择文件: C:/Users/<USER>/Desktop/BR.txt
[2025-07-30 23:20:44] [INFO] 解析结果: 未使用账户 12个, 成功账户 0个, 失败账户 0个
[2025-07-30 23:20:44] [INFO] 从文件中解析出 12 个账户
[2025-07-30 23:20:44] [DEBUG] 已排除 0 个成功登录的账户
[2025-07-30 23:20:44] [DEBUG] 已排除 0 个登录失败的账户
[2025-07-30 23:20:44] [DEBUG] 筛选后可用账户数量: 12
[2025-07-30 23:20:44] [INFO] 开始处理 1 个账户
[2025-07-30 23:20:44] [INFO] 账户 <EMAIL> 状态: 登录中
[2025-07-30 23:20:48] [SUCCESS] 账户 <EMAIL> 登录成功
[2025-07-30 23:20:48] [SUCCESS] 账户 <EMAIL> 已标记为登录成功
[2025-07-30 23:20:48] [INFO] 账户 <EMAIL> 状态: 登录成功
[2025-07-30 23:20:48] [SUCCESS] 处理完成，成功登录 1 个账户
[2025-07-30 23:20:48] [DEBUG] 调试：成功账户邮箱列表 ['<EMAIL>']
[2025-07-30 23:20:48] [DEBUG] 调试：失败账户邮箱列表 []
[2025-07-30 23:20:48] [INFO] 文件更新完成：成功账户 1，失败账户 0，未处理账户 11
[2025-07-30 23:20:48] [DEBUG] 调试信息：成功账户列表长度 1
[2025-07-30 23:20:48] [DEBUG] 调试信息：第一个成功账户 ['<EMAIL>', 'E4aT737wmII', 'M.C541_BL2.0.U.-CsZjU7cwC7VM9*A5A13pCt13BZXU2GrmIorcdDigS8TrPfkOAeh7W4b7LBG3MNnVBZWMOfdgvtzGHgBt3!LWxWtSZ5AlL3sCgQCLtdJUkuLvk68DTnejKdy2qK*VI1hsiAi0Hwl19EKlYCYu8z4VM4hh7xr7A2fO4K*xkc!KWyYYH2ewvZZUbYZt7NHaatwQPZyguljHFUqyC9Wl50jmVhFKWxdgplBu71uipjKnXkswU2uBYpkKsCbYfQEcgOIlm2rXTevDDs3cKqVbjeuYMU5i*bVGvrymVZr1Duw8eetVFB1YVEbUs2ODSOsYLdta0RQPBfr9Hl6k2rqhw!XQxXkZ4iQcTJaifTaAEmI1Bc!43kZWCJqq3CHQokZh6deCBdAjzTBnVSy9uLyKCgp0OggQKb0H72P54uJns8C8zWql', '9e5f94bc-e8a4-4e73-b8be-63364c29d753']
[2025-07-30 23:20:48] [INFO] 已更新文件内容
[2025-07-30 23:20:48] [INFO] 更新剩余账户数量: 11
[2025-07-30 23:20:49] [INFO] 尝试为 <EMAIL> 查询状态
[2025-07-30 23:20:49] [DEBUG] 初始化账户状态查询器: <EMAIL>
[2025-07-30 23:20:49] [INFO] 成功启动状态查询线程: <EMAIL>
[2025-07-30 23:20:55] [DEBUG] 更新状态查询结果: 行0, 状态状态查询成功, 结果无100$账号
[2025-07-30 23:20:55] [INFO] 账户无100$状态: 行1
[2025-07-30 23:20:55] [DEBUG] 已恢复行0的按钮状态
[2025-07-30 23:20:55] [INFO] 行 1: 状态查询成功, 结果: 无100$账号
[2025-07-30 23:20:55] [DEBUG] 线程仍在运行，稍后清理: 行0
[2025-07-30 23:21:00] [ERROR] 延迟清理线程引用时出错: wrapped C/C++ object of type QThread has been deleted
[2025-07-31 10:44:11] [INFO] 停止行 0 的验证码获取线程
[2025-07-31 10:44:11] [INFO] 已停止文件监控
[2025-07-31 10:44:11] [INFO] === AWS工具关闭 ===
[2025-07-31 11:00:56] [INFO] 设置Qt插件路径: D:\Python311\Lib\site-packages\PyQt5\Qt5\plugins
[2025-07-31 11:00:56] [INFO] === AWS工具启动 ===
[2025-07-31 11:00:56] [INFO] Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
[2025-07-31 11:00:56] [INFO] 系统平台: win32
[2025-07-31 11:00:56] [INFO] 已设置Windows任务栏图标 AppID: aws.verification.tool.1.0
[2025-07-31 11:00:56] [INFO] AWS文件监控已启动
[2025-07-31 11:00:56] [INFO] 已设置Windows任务栏图标 AppID: aws.verification.tool.1.0
[2025-07-31 11:00:56] [INFO] === AWS工具启动 ===
[2025-07-31 11:00:56] [INFO] Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
[2025-07-31 11:00:56] [INFO] 系统平台: win32
[2025-07-31 11:00:57] [INFO] 软件已初始化，请按照上方指南操作
[2025-07-31 11:01:00] [INFO] 更新剩余账户数量: 12
[2025-07-31 11:01:00] [INFO] 已选择文件: C:/Users/<USER>/Desktop/BR.txt
[2025-07-31 11:01:01] [INFO] 未指定账户数量，将加载全部账户
[2025-07-31 11:01:01] [INFO] 解析结果: 未使用账户 12个, 成功账户 0个, 失败账户 0个
[2025-07-31 11:01:01] [INFO] 从文件中解析出 12 个账户
[2025-07-31 11:01:01] [DEBUG] 已排除 0 个成功登录的账户
[2025-07-31 11:01:01] [DEBUG] 已排除 0 个登录失败的账户
[2025-07-31 11:01:01] [DEBUG] 筛选后可用账户数量: 12
[2025-07-31 11:01:03] [WARNING] 请求的账户数量(999999)超过可用账户数量(12)
[2025-07-31 11:01:03] [INFO] 开始处理 12 个账户
[2025-07-31 11:01:03] [INFO] 账户 <EMAIL> 状态: 登录中
[2025-07-31 11:01:06] [SUCCESS] 账户 <EMAIL> 登录成功
[2025-07-31 11:01:06] [SUCCESS] 账户 <EMAIL> 已标记为登录成功
[2025-07-31 11:01:06] [INFO] 账户 <EMAIL> 状态: 登录成功
[2025-07-31 11:01:06] [INFO] 账户 <EMAIL> 状态: 登录中
[2025-07-31 11:01:11] [SUCCESS] 账户 <EMAIL> 登录成功
[2025-07-31 11:01:11] [SUCCESS] 账户 <EMAIL> 已标记为登录成功
[2025-07-31 11:01:11] [INFO] 账户 <EMAIL> 状态: 登录成功
[2025-07-31 11:01:11] [INFO] 账户 <EMAIL> 状态: 登录中
[2025-07-31 11:01:14] [SUCCESS] 账户 <EMAIL> 登录成功
[2025-07-31 11:01:15] [SUCCESS] 账户 <EMAIL> 已标记为登录成功
[2025-07-31 11:01:15] [INFO] 账户 <EMAIL> 状态: 登录成功
[2025-07-31 11:01:15] [INFO] 账户 <EMAIL> 状态: 登录中
[2025-07-31 11:01:18] [SUCCESS] 账户 <EMAIL> 登录成功
[2025-07-31 11:01:18] [SUCCESS] 账户 <EMAIL> 已标记为登录成功
[2025-07-31 11:01:18] [INFO] 账户 <EMAIL> 状态: 登录成功
[2025-07-31 11:01:18] [INFO] 账户 <EMAIL> 状态: 登录中
[2025-07-31 11:01:22] [SUCCESS] 账户 <EMAIL> 登录成功
[2025-07-31 11:01:22] [SUCCESS] 账户 <EMAIL> 已标记为登录成功
[2025-07-31 11:01:22] [INFO] 账户 <EMAIL> 状态: 登录成功
[2025-07-31 11:01:22] [INFO] 账户 <EMAIL> 状态: 登录中
[2025-07-31 11:01:27] [SUCCESS] 账户 <EMAIL> 登录成功
[2025-07-31 11:01:27] [SUCCESS] 账户 <EMAIL> 已标记为登录成功
[2025-07-31 11:01:27] [INFO] 账户 <EMAIL> 状态: 登录成功
[2025-07-31 11:01:27] [INFO] 账户 <EMAIL> 状态: 登录中
[2025-07-31 11:01:31] [SUCCESS] 账户 <EMAIL> 登录成功
[2025-07-31 11:01:31] [SUCCESS] 账户 <EMAIL> 已标记为登录成功
[2025-07-31 11:01:31] [INFO] 账户 <EMAIL> 状态: 登录成功
[2025-07-31 11:01:31] [INFO] 账户 <EMAIL> 状态: 登录中
[2025-07-31 11:01:35] [SUCCESS] 账户 <EMAIL> 登录成功
[2025-07-31 11:01:35] [SUCCESS] 账户 <EMAIL> 已标记为登录成功
[2025-07-31 11:01:35] [INFO] 账户 <EMAIL> 状态: 登录成功
[2025-07-31 11:01:35] [INFO] 账户 <EMAIL> 状态: 登录中
[2025-07-31 11:01:39] [SUCCESS] 账户 <EMAIL> 登录成功
[2025-07-31 11:01:39] [SUCCESS] 账户 <EMAIL> 已标记为登录成功
[2025-07-31 11:01:39] [INFO] 账户 <EMAIL> 状态: 登录成功
[2025-07-31 11:01:39] [INFO] 账户 <EMAIL> 状态: 登录中
[2025-07-31 11:01:45] [SUCCESS] 账户 <EMAIL> 登录成功
[2025-07-31 11:01:45] [SUCCESS] 账户 <EMAIL> 已标记为登录成功
[2025-07-31 11:01:45] [INFO] 账户 <EMAIL> 状态: 登录成功
[2025-07-31 11:01:45] [INFO] 账户 <EMAIL> 状态: 登录中
[2025-07-31 11:01:49] [SUCCESS] 账户 <EMAIL> 登录成功
[2025-07-31 11:01:49] [SUCCESS] 账户 <EMAIL> 已标记为登录成功
[2025-07-31 11:01:49] [INFO] 账户 <EMAIL> 状态: 登录成功
[2025-07-31 11:01:49] [INFO] 账户 <EMAIL> 状态: 登录中
[2025-07-31 11:01:53] [SUCCESS] 账户 <EMAIL> 登录成功
[2025-07-31 11:01:53] [SUCCESS] 账户 <EMAIL> 已标记为登录成功
[2025-07-31 11:01:53] [INFO] 账户 <EMAIL> 状态: 登录成功
[2025-07-31 11:01:53] [SUCCESS] 处理完成，成功登录 12 个账户
[2025-07-31 11:01:53] [DEBUG] 调试：成功账户邮箱列表 ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']
[2025-07-31 11:01:53] [DEBUG] 调试：失败账户邮箱列表 []
[2025-07-31 11:01:53] [INFO] 文件更新完成：成功账户 12，失败账户 0，未处理账户 0
[2025-07-31 11:01:53] [DEBUG] 调试信息：成功账户列表长度 12
[2025-07-31 11:01:53] [DEBUG] 调试信息：第一个成功账户 ['<EMAIL>', 'E4aT737wmII', 'M.C541_BL2.0.U.-CsZjU7cwC7VM9*A5A13pCt13BZXU2GrmIorcdDigS8TrPfkOAeh7W4b7LBG3MNnVBZWMOfdgvtzGHgBt3!LWxWtSZ5AlL3sCgQCLtdJUkuLvk68DTnejKdy2qK*VI1hsiAi0Hwl19EKlYCYu8z4VM4hh7xr7A2fO4K*xkc!KWyYYH2ewvZZUbYZt7NHaatwQPZyguljHFUqyC9Wl50jmVhFKWxdgplBu71uipjKnXkswU2uBYpkKsCbYfQEcgOIlm2rXTevDDs3cKqVbjeuYMU5i*bVGvrymVZr1Duw8eetVFB1YVEbUs2ODSOsYLdta0RQPBfr9Hl6k2rqhw!XQxXkZ4iQcTJaifTaAEmI1Bc!43kZWCJqq3CHQokZh6deCBdAjzTBnVSy9uLyKCgp0OggQKb0H72P54uJns8C8zWql', '9e5f94bc-e8a4-4e73-b8be-63364c29d753']
[2025-07-31 11:01:53] [INFO] 已更新文件内容
[2025-07-31 11:01:53] [INFO] 更新剩余账户数量: 0
[2025-07-31 11:01:55] [INFO] 开始一键查询 12 个账户的状态
[2025-07-31 11:01:55] [INFO] 尝试为 <EMAIL> 查询状态
[2025-07-31 11:01:55] [DEBUG] 初始化账户状态查询器: <EMAIL>
[2025-07-31 11:01:55] [INFO] 成功启动状态查询线程: <EMAIL>
[2025-07-31 11:01:57] [INFO] 尝试为 <EMAIL> 查询状态
[2025-07-31 11:01:57] [DEBUG] 初始化账户状态查询器: <EMAIL>
[2025-07-31 11:01:57] [INFO] 成功启动状态查询线程: <EMAIL>
[2025-07-31 11:01:59] [INFO] 尝试为 <EMAIL> 查询状态
[2025-07-31 11:01:59] [DEBUG] 初始化账户状态查询器: <EMAIL>
[2025-07-31 11:01:59] [INFO] 成功启动状态查询线程: <EMAIL>
[2025-07-31 11:02:01] [DEBUG] 更新状态查询结果: 行0, 状态状态查询成功, 结果无100$账号
[2025-07-31 11:02:01] [INFO] 账户无100$状态: 行1
[2025-07-31 11:02:01] [DEBUG] 已恢复行0的按钮状态
[2025-07-31 11:02:01] [INFO] 行 1: 状态查询成功, 结果: 无100$账号
[2025-07-31 11:02:01] [DEBUG] 线程仍在运行，稍后清理: 行0
[2025-07-31 11:02:01] [INFO] 尝试为 <EMAIL> 查询状态
[2025-07-31 11:02:01] [DEBUG] 初始化账户状态查询器: <EMAIL>
[2025-07-31 11:02:01] [INFO] 成功启动状态查询线程: <EMAIL>
[2025-07-31 11:02:03] [INFO] 尝试为 <EMAIL> 查询状态
[2025-07-31 11:02:03] [DEBUG] 初始化账户状态查询器: <EMAIL>
[2025-07-31 11:02:03] [INFO] 成功启动状态查询线程: <EMAIL>
[2025-07-31 11:02:05] [DEBUG] 更新状态查询结果: 行1, 状态状态查询成功, 结果无100$账号
[2025-07-31 11:02:05] [INFO] 账户无100$状态: 行2
[2025-07-31 11:02:05] [DEBUG] 已恢复行1的按钮状态
[2025-07-31 11:02:05] [INFO] 行 2: 状态查询成功, 结果: 无100$账号
[2025-07-31 11:02:05] [DEBUG] 线程仍在运行，稍后清理: 行1
[2025-07-31 11:02:05] [INFO] 尝试为 <EMAIL> 查询状态
[2025-07-31 11:02:05] [DEBUG] 初始化账户状态查询器: <EMAIL>
[2025-07-31 11:02:05] [INFO] 成功启动状态查询线程: <EMAIL>
[2025-07-31 11:02:07] [DEBUG] 更新状态查询结果: 行2, 状态状态查询成功, 结果无100$账号
[2025-07-31 11:02:07] [INFO] 账户无100$状态: 行3
[2025-07-31 11:02:07] [DEBUG] 已恢复行2的按钮状态
[2025-07-31 11:02:07] [INFO] 行 3: 状态查询成功, 结果: 无100$账号
[2025-07-31 11:02:07] [DEBUG] 线程仍在运行，稍后清理: 行2
[2025-07-31 11:02:07] [DEBUG] 更新状态查询结果: 行3, 状态状态查询成功, 结果无100$账号
[2025-07-31 11:02:07] [INFO] 账户无100$状态: 行4
[2025-07-31 11:02:07] [DEBUG] 已恢复行3的按钮状态
[2025-07-31 11:02:07] [INFO] 行 4: 状态查询成功, 结果: 无100$账号
[2025-07-31 11:02:07] [DEBUG] 线程仍在运行，稍后清理: 行3
[2025-07-31 11:02:07] [ERROR] 延迟清理线程引用时出错: wrapped C/C++ object of type QThread has been deleted
[2025-07-31 11:02:07] [INFO] 尝试为 <EMAIL> 查询状态
[2025-07-31 11:02:07] [DEBUG] 初始化账户状态查询器: <EMAIL>
[2025-07-31 11:02:07] [INFO] 成功启动状态查询线程: <EMAIL>
[2025-07-31 11:02:09] [DEBUG] 更新状态查询结果: 行4, 状态状态查询成功, 结果无100$账号
[2025-07-31 11:02:09] [INFO] 账户无100$状态: 行5
[2025-07-31 11:02:09] [DEBUG] 已恢复行4的按钮状态
[2025-07-31 11:02:09] [INFO] 行 5: 状态查询成功, 结果: 无100$账号
[2025-07-31 11:02:09] [DEBUG] 线程仍在运行，稍后清理: 行4
[2025-07-31 11:02:09] [INFO] 尝试为 <EMAIL> 查询状态
[2025-07-31 11:02:09] [DEBUG] 初始化账户状态查询器: <EMAIL>
[2025-07-31 11:02:09] [INFO] 成功启动状态查询线程: <EMAIL>
[2025-07-31 11:02:11] [DEBUG] 更新状态查询结果: 行5, 状态状态查询成功, 结果无100$账号
[2025-07-31 11:02:11] [INFO] 账户无100$状态: 行6
[2025-07-31 11:02:11] [DEBUG] 已恢复行5的按钮状态
[2025-07-31 11:02:11] [INFO] 行 6: 状态查询成功, 结果: 无100$账号
[2025-07-31 11:02:11] [DEBUG] 线程仍在运行，稍后清理: 行5
[2025-07-31 11:02:11] [ERROR] 延迟清理线程引用时出错: wrapped C/C++ object of type QThread has been deleted
[2025-07-31 11:02:11] [INFO] 尝试为 <EMAIL> 查询状态
[2025-07-31 11:02:11] [DEBUG] 初始化账户状态查询器: <EMAIL>
[2025-07-31 11:02:11] [INFO] 成功启动状态查询线程: <EMAIL>
[2025-07-31 11:02:13] [DEBUG] 更新状态查询结果: 行6, 状态状态查询成功, 结果无100$账号
[2025-07-31 11:02:13] [INFO] 账户无100$状态: 行7
[2025-07-31 11:02:13] [DEBUG] 已恢复行6的按钮状态
[2025-07-31 11:02:13] [INFO] 行 7: 状态查询成功, 结果: 无100$账号
[2025-07-31 11:02:13] [DEBUG] 线程仍在运行，稍后清理: 行6
[2025-07-31 11:02:13] [ERROR] 延迟清理线程引用时出错: wrapped C/C++ object of type QThread has been deleted
[2025-07-31 11:02:13] [ERROR] 延迟清理线程引用时出错: wrapped C/C++ object of type QThread has been deleted
[2025-07-31 11:02:13] [INFO] 尝试为 <EMAIL> 查询状态
[2025-07-31 11:02:13] [DEBUG] 初始化账户状态查询器: <EMAIL>
[2025-07-31 11:02:13] [INFO] 成功启动状态查询线程: <EMAIL>
[2025-07-31 11:02:15] [DEBUG] 更新状态查询结果: 行7, 状态状态查询成功, 结果无100$账号
[2025-07-31 11:02:15] [INFO] 账户无100$状态: 行8
[2025-07-31 11:02:15] [DEBUG] 已恢复行7的按钮状态
[2025-07-31 11:02:15] [INFO] 行 8: 状态查询成功, 结果: 无100$账号
[2025-07-31 11:02:15] [DEBUG] 线程仍在运行，稍后清理: 行7
[2025-07-31 11:02:15] [ERROR] 延迟清理线程引用时出错: wrapped C/C++ object of type QThread has been deleted
[2025-07-31 11:02:15] [INFO] 尝试为 <EMAIL> 查询状态
[2025-07-31 11:02:15] [DEBUG] 初始化账户状态查询器: <EMAIL>
[2025-07-31 11:02:15] [INFO] 成功启动状态查询线程: <EMAIL>
[2025-07-31 11:02:17] [DEBUG] 更新状态查询结果: 行8, 状态状态查询成功, 结果无100$账号
[2025-07-31 11:02:17] [INFO] 账户无100$状态: 行9
[2025-07-31 11:02:17] [DEBUG] 已恢复行8的按钮状态
[2025-07-31 11:02:17] [INFO] 行 9: 状态查询成功, 结果: 无100$账号
[2025-07-31 11:02:17] [DEBUG] 线程仍在运行，稍后清理: 行8
[2025-07-31 11:02:18] [ERROR] 延迟清理线程引用时出错: wrapped C/C++ object of type QThread has been deleted
[2025-07-31 11:02:18] [INFO] 尝试为 <EMAIL> 查询状态
[2025-07-31 11:02:18] [DEBUG] 初始化账户状态查询器: <EMAIL>
[2025-07-31 11:02:18] [INFO] 成功启动状态查询线程: <EMAIL>
[2025-07-31 11:02:20] [DEBUG] 更新状态查询结果: 行9, 状态状态查询成功, 结果无100$账号
[2025-07-31 11:02:20] [INFO] 账户无100$状态: 行10
[2025-07-31 11:02:20] [DEBUG] 已恢复行9的按钮状态
[2025-07-31 11:02:20] [INFO] 行 10: 状态查询成功, 结果: 无100$账号
[2025-07-31 11:02:20] [DEBUG] 线程仍在运行，稍后清理: 行9
[2025-07-31 11:02:20] [ERROR] 延迟清理线程引用时出错: wrapped C/C++ object of type QThread has been deleted
[2025-07-31 11:02:20] [SUCCESS] 一键查询状态完成
[2025-07-31 11:02:20] [DEBUG] 更新状态查询结果: 行10, 状态状态查询成功, 结果无100$账号
[2025-07-31 11:02:20] [INFO] 账户无100$状态: 行11
[2025-07-31 11:02:20] [DEBUG] 已恢复行10的按钮状态
[2025-07-31 11:02:20] [INFO] 行 11: 状态查询成功, 结果: 无100$账号
[2025-07-31 11:02:20] [DEBUG] 线程仍在运行，稍后清理: 行10
[2025-07-31 11:02:20] [ERROR] 延迟清理线程引用时出错: wrapped C/C++ object of type QThread has been deleted
[2025-07-31 11:02:22] [ERROR] 延迟清理线程引用时出错: wrapped C/C++ object of type QThread has been deleted
[2025-07-31 11:02:23] [DEBUG] 更新状态查询结果: 行11, 状态状态查询成功, 结果无100$账号
[2025-07-31 11:02:23] [INFO] 账户无100$状态: 行12
[2025-07-31 11:02:23] [DEBUG] 已恢复行11的按钮状态
[2025-07-31 11:02:23] [INFO] 行 12: 状态查询成功, 结果: 无100$账号
[2025-07-31 11:02:23] [DEBUG] 线程仍在运行，稍后清理: 行11
[2025-07-31 11:02:25] [ERROR] 延迟清理线程引用时出错: wrapped C/C++ object of type QThread has been deleted
[2025-07-31 11:02:25] [ERROR] 延迟清理线程引用时出错: wrapped C/C++ object of type QThread has been deleted
[2025-07-31 11:02:28] [ERROR] 延迟清理线程引用时出错: wrapped C/C++ object of type QThread has been deleted
[2025-07-31 11:02:58] [INFO] 停止行 0 的验证码获取线程
[2025-07-31 11:02:58] [INFO] 停止行 1 的验证码获取线程
[2025-07-31 11:02:58] [INFO] 停止行 2 的验证码获取线程
[2025-07-31 11:02:58] [INFO] 停止行 3 的验证码获取线程
[2025-07-31 11:02:58] [INFO] 停止行 4 的验证码获取线程
[2025-07-31 11:02:58] [INFO] 停止行 5 的验证码获取线程
[2025-07-31 11:02:58] [INFO] 停止行 6 的验证码获取线程
[2025-07-31 11:02:58] [INFO] 停止行 7 的验证码获取线程
[2025-07-31 11:02:58] [INFO] 停止行 8 的验证码获取线程
[2025-07-31 11:02:58] [INFO] 停止行 9 的验证码获取线程
[2025-07-31 11:02:58] [INFO] 停止行 10 的验证码获取线程
[2025-07-31 11:02:58] [INFO] 停止行 11 的验证码获取线程
[2025-07-31 11:02:58] [INFO] 已停止文件监控
[2025-07-31 11:02:58] [INFO] === AWS工具关闭 ===
